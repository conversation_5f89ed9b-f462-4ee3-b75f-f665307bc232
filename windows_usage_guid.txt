
Windows Flags (locker.exe): 

    --no-admin
       Disables check for admin rights. Otherwise program will not execute, if not elevated.
       We strongly discourage executing program from not elevated command prompt.

    --no-priority
       Disables CPU and IO priority setting.
	   
	--Directory 
		to encrypt a specific directory
	    e.g.: locker.exe --Directory c:\path\to\data
	--File
		to encrypt a specific file 
	    e.g.: locker.exe --File c:\path\to\data\data.zip
	--Driver
		to encrypt a specific drive
        e.g.: locker.exe --Drive c:\\

      WARNING: do not leave white spaces before and after the separator

      TIP:
      if target encryption isn't working, but you sure that files exists inside provided directory
      try to disable filters using one of no-*f flags (read documentation for no-*f flags below)
      P.S. only path/paths supplied with this flag will be processed. Other paths, shares will be omitted.


	--no-autostart
      Disable self-autostart feature for all users.

    --no-wallpaper
      Disable wallpaper/lockscreen change.

    --no-local
       Disables encryption of local volumes.

    --no-mounted
       Disables encryption of mounted shares and found network shortcuts.

    --no-network
      Disable search of hosts and their network resources in whole subnet (for domain + non-domain joined computers).
      WARNING: to completely disable encryption of network resources, you must specify 3 flags: --no-mounted --no-domain --no-network

    --force
       Does not create a named mutex (allows you to run several instances of software on one host)
       We strongly do not recommend running multiple instances to encrypt one host
       An example of the correct use of the flag - use for self-spread to hosts using --spread\--spread-vcenter (the corresponding flags can be found above)

    --no-logs
       Disables writing logs to log-files.
	   
    --spread-smb
      Starts locker in spreading mode then exits (spread itself to Windows hosts)
      On start program goes into interactive mode and asks for following parameters:
        - path to a list of Windows hosts (can use drag-and-drop) with login/pass in format host:user:pass on newline each. For example:
          ***********:Administrator:P@ssw0rd
          Veeam.domain.com:<EMAIL>:P@ssw0rd
      If list is not supplied, then scans for ALL hosts in a domain and tries to access them (PsExec) using accounts entered during build creation as well as current account.
      Each host is processed in separate thread (with limit), thus decreasing processing time dramatically.
      All execution flags supplied are copied to other machines.
      All supplied accounts are tried on each host, until successful execution is detected.
      If all accounts failed or no account list was supplied, then local account is used.
      Once the program propagated itself to remote host, it does not try to propagate itself further from that host.
      Once all hosts were processed, program will print lists of spreading results and exit, without encrypting local computer.

    --no-extension
      Allows to NOT change file extension, otherwise extension is changed to unique value for each company.
      During file encryption, file is still temporarily renamed. After file encryption is complete its name is reverted back to original.

    --no-note
      Disables note saving in every folder.

    --no-proc
      Disable process killer.

    --no-services
      Disable service killer.

    --no-vm
      Disable VM killer.

    --sandbox-check
      Disable sandbox detection. Whether program is executed in virtual environment.
      If sandbox is detected, some services and processes are not killed, to preserve system stability.

    --no-killcluster
      Disable cluster killer.



Windows Flags (decrypter.exe): 

	--Key
		Private key for decryption
	
	--Directory 
		to decrypt a specific directory
	
	--File
		to decrypt a specific file 
	
	--Driver
		to decrypt a specific drive
