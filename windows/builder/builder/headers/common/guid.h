
int guidSize = 4138;

unsigned char guid[4138] = {
	0x0D, 0x0A, 0x57, 0x69, 0x6E, 0x64, 0x6F, 0x77, 0x73, 0x20, 0x46, 0x6C,
	0x61, 0x67, 0x73, 0x20, 0x28, 0x6C, 0x6F, 0x63, 0x6B, 0x65, 0x72, 0x2E,
	0x65, 0x78, 0x65, 0x29, 0x3A, 0x20, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20,
	0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x61, 0x64, 0x6D, 0x69, 0x6E,
	0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6C, 0x65, 0x73, 0x20, 0x63, 0x68, 0x65, 0x63, 0x6B, 0x20,
	0x66, 0x6F, 0x72, 0x20, 0x61, 0x64, 0x6D, 0x69, 0x6E, 0x20, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2E, 0x20, 0x4F, 0x74, 0x68, 0x65, 0x72, 0x77,
	0x69, 0x73, 0x65, 0x20, 0x70, 0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x20,
	0x77, 0x69, 0x6C, 0x6C, 0x20, 0x6E, 0x6F, 0x74, 0x20, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x2C, 0x20, 0x69, 0x66, 0x20, 0x6E, 0x6F, 0x74,
	0x20, 0x65, 0x6C, 0x65, 0x76, 0x61, 0x74, 0x65, 0x64, 0x2E, 0x0D, 0x0A,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x57, 0x65, 0x20, 0x73, 0x74,
	0x72, 0x6F, 0x6E, 0x67, 0x6C, 0x79, 0x20, 0x64, 0x69, 0x73, 0x63, 0x6F,
	0x75, 0x72, 0x61, 0x67, 0x65, 0x20, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6E, 0x67, 0x20, 0x70, 0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x20,
	0x66, 0x72, 0x6F, 0x6D, 0x20, 0x6E, 0x6F, 0x74, 0x20, 0x65, 0x6C, 0x65,
	0x76, 0x61, 0x74, 0x65, 0x64, 0x20, 0x63, 0x6F, 0x6D, 0x6D, 0x61, 0x6E,
	0x64, 0x20, 0x70, 0x72, 0x6F, 0x6D, 0x70, 0x74, 0x2E, 0x0D, 0x0A, 0x0D,
	0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x70, 0x72,
	0x69, 0x6F, 0x72, 0x69, 0x74, 0x79, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C, 0x65, 0x73, 0x20,
	0x43, 0x50, 0x55, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x49, 0x4F, 0x20, 0x70,
	0x72, 0x69, 0x6F, 0x72, 0x69, 0x74, 0x79, 0x20, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6E, 0x67, 0x2E, 0x0D, 0x0A, 0x09, 0x20, 0x20, 0x20, 0x0D, 0x0A,
	0x09, 0x2D, 0x2D, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6F, 0x72, 0x79,
	0x20, 0x0D, 0x0A, 0x09, 0x09, 0x74, 0x6F, 0x20, 0x65, 0x6E, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x20, 0x61, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x20, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6F, 0x72, 0x79,
	0x0D, 0x0A, 0x09, 0x20, 0x20, 0x20, 0x20, 0x65, 0x2E, 0x67, 0x2E, 0x3A,
	0x20, 0x6C, 0x6F, 0x63, 0x6B, 0x65, 0x72, 0x2E, 0x65, 0x78, 0x65, 0x20,
	0x2D, 0x2D, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6F, 0x72, 0x79, 0x20,
	0x63, 0x3A, 0x5C, 0x70, 0x61, 0x74, 0x68, 0x5C, 0x74, 0x6F, 0x5C, 0x64,
	0x61, 0x74, 0x61, 0x0D, 0x0A, 0x09, 0x2D, 0x2D, 0x46, 0x69, 0x6C, 0x65,
	0x0D, 0x0A, 0x09, 0x09, 0x74, 0x6F, 0x20, 0x65, 0x6E, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x20, 0x61, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x20, 0x66, 0x69, 0x6C, 0x65, 0x20, 0x0D, 0x0A, 0x09, 0x20, 0x20,
	0x20, 0x20, 0x65, 0x2E, 0x67, 0x2E, 0x3A, 0x20, 0x6C, 0x6F, 0x63, 0x6B,
	0x65, 0x72, 0x2E, 0x65, 0x78, 0x65, 0x20, 0x2D, 0x2D, 0x46, 0x69, 0x6C,
	0x65, 0x20, 0x63, 0x3A, 0x5C, 0x70, 0x61, 0x74, 0x68, 0x5C, 0x74, 0x6F,
	0x5C, 0x64, 0x61, 0x74, 0x61, 0x5C, 0x64, 0x61, 0x74, 0x61, 0x2E, 0x7A,
	0x69, 0x70, 0x0D, 0x0A, 0x09, 0x2D, 0x2D, 0x44, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x0D, 0x0A, 0x09, 0x09, 0x74, 0x6F, 0x20, 0x65, 0x6E, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x20, 0x61, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x20, 0x64, 0x72, 0x69, 0x76, 0x65, 0x0D, 0x0A, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x65, 0x2E, 0x67, 0x2E, 0x3A, 0x20,
	0x6C, 0x6F, 0x63, 0x6B, 0x65, 0x72, 0x2E, 0x65, 0x78, 0x65, 0x20, 0x2D,
	0x2D, 0x44, 0x72, 0x69, 0x76, 0x65, 0x20, 0x63, 0x3A, 0x5C, 0x5C, 0x0D,
	0x0A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x57, 0x41, 0x52,
	0x4E, 0x49, 0x4E, 0x47, 0x3A, 0x20, 0x64, 0x6F, 0x20, 0x6E, 0x6F, 0x74,
	0x20, 0x6C, 0x65, 0x61, 0x76, 0x65, 0x20, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x20, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x20, 0x62, 0x65, 0x66, 0x6F,
	0x72, 0x65, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x73, 0x65, 0x70, 0x61, 0x72, 0x61, 0x74,
	0x6F, 0x72, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x54, 0x49, 0x50, 0x3A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x69, 0x66, 0x20, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x20, 0x65, 0x6E,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6F, 0x6E, 0x20, 0x69, 0x73, 0x6E,
	0x27, 0x74, 0x20, 0x77, 0x6F, 0x72, 0x6B, 0x69, 0x6E, 0x67, 0x2C, 0x20,
	0x62, 0x75, 0x74, 0x20, 0x79, 0x6F, 0x75, 0x20, 0x73, 0x75, 0x72, 0x65,
	0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x66, 0x69, 0x6C, 0x65, 0x73, 0x20,
	0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x20, 0x69, 0x6E, 0x73, 0x69, 0x64,
	0x65, 0x20, 0x70, 0x72, 0x6F, 0x76, 0x69, 0x64, 0x65, 0x64, 0x20, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6F, 0x72, 0x79, 0x0D, 0x0A, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x74, 0x72, 0x79, 0x20, 0x74, 0x6F, 0x20, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6C, 0x65, 0x20, 0x66, 0x69, 0x6C, 0x74, 0x65,
	0x72, 0x73, 0x20, 0x75, 0x73, 0x69, 0x6E, 0x67, 0x20, 0x6F, 0x6E, 0x65,
	0x20, 0x6F, 0x66, 0x20, 0x6E, 0x6F, 0x2D, 0x2A, 0x66, 0x20, 0x66, 0x6C,
	0x61, 0x67, 0x73, 0x20, 0x28, 0x72, 0x65, 0x61, 0x64, 0x20, 0x64, 0x6F,
	0x63, 0x75, 0x6D, 0x65, 0x6E, 0x74, 0x61, 0x74, 0x69, 0x6F, 0x6E, 0x20,
	0x66, 0x6F, 0x72, 0x20, 0x6E, 0x6F, 0x2D, 0x2A, 0x66, 0x20, 0x66, 0x6C,
	0x61, 0x67, 0x73, 0x20, 0x62, 0x65, 0x6C, 0x6F, 0x77, 0x29, 0x0D, 0x0A,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x50, 0x2E, 0x53, 0x2E, 0x20, 0x6F,
	0x6E, 0x6C, 0x79, 0x20, 0x70, 0x61, 0x74, 0x68, 0x2F, 0x70, 0x61, 0x74,
	0x68, 0x73, 0x20, 0x73, 0x75, 0x70, 0x70, 0x6C, 0x69, 0x65, 0x64, 0x20,
	0x77, 0x69, 0x74, 0x68, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x66, 0x6C,
	0x61, 0x67, 0x20, 0x77, 0x69, 0x6C, 0x6C, 0x20, 0x62, 0x65, 0x20, 0x70,
	0x72, 0x6F, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x2E, 0x20, 0x4F, 0x74,
	0x68, 0x65, 0x72, 0x20, 0x70, 0x61, 0x74, 0x68, 0x73, 0x2C, 0x20, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x73, 0x20, 0x77, 0x69, 0x6C, 0x6C, 0x20, 0x62,
	0x65, 0x20, 0x6F, 0x6D, 0x69, 0x74, 0x74, 0x65, 0x64, 0x2E, 0x0D, 0x0A,
	0x0D, 0x0A, 0x0D, 0x0A, 0x09, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x61, 0x75,
	0x74, 0x6F, 0x73, 0x74, 0x61, 0x72, 0x74, 0x0D, 0x0A, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C, 0x65, 0x20, 0x73,
	0x65, 0x6C, 0x66, 0x2D, 0x61, 0x75, 0x74, 0x6F, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x20, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x20, 0x66, 0x6F,
	0x72, 0x20, 0x61, 0x6C, 0x6C, 0x20, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2E,
	0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F,
	0x2D, 0x77, 0x61, 0x6C, 0x6C, 0x70, 0x61, 0x70, 0x65, 0x72, 0x0D, 0x0A,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C,
	0x65, 0x20, 0x77, 0x61, 0x6C, 0x6C, 0x70, 0x61, 0x70, 0x65, 0x72, 0x2F,
	0x6C, 0x6F, 0x63, 0x6B, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6E, 0x20, 0x63,
	0x68, 0x61, 0x6E, 0x67, 0x65, 0x2E, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20,
	0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x6C, 0x6F, 0x63, 0x61, 0x6C,
	0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6C, 0x65, 0x73, 0x20, 0x65, 0x6E, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x69, 0x6F, 0x6E, 0x20, 0x6F, 0x66, 0x20, 0x6C, 0x6F, 0x63, 0x61,
	0x6C, 0x20, 0x76, 0x6F, 0x6C, 0x75, 0x6D, 0x65, 0x73, 0x2E, 0x0D, 0x0A,
	0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x6D,
	0x6F, 0x75, 0x6E, 0x74, 0x65, 0x64, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C, 0x65, 0x73, 0x20,
	0x65, 0x6E, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6F, 0x6E, 0x20, 0x6F,
	0x66, 0x20, 0x6D, 0x6F, 0x75, 0x6E, 0x74, 0x65, 0x64, 0x20, 0x73, 0x68,
	0x61, 0x72, 0x65, 0x73, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x66, 0x6F, 0x75,
	0x6E, 0x64, 0x20, 0x6E, 0x65, 0x74, 0x77, 0x6F, 0x72, 0x6B, 0x20, 0x73,
	0x68, 0x6F, 0x72, 0x74, 0x63, 0x75, 0x74, 0x73, 0x2E, 0x0D, 0x0A, 0x0D,
	0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x6E, 0x65,
	0x74, 0x77, 0x6F, 0x72, 0x6B, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C, 0x65, 0x20, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x20, 0x6F, 0x66, 0x20, 0x68, 0x6F, 0x73, 0x74, 0x73,
	0x20, 0x61, 0x6E, 0x64, 0x20, 0x74, 0x68, 0x65, 0x69, 0x72, 0x20, 0x6E,
	0x65, 0x74, 0x77, 0x6F, 0x72, 0x6B, 0x20, 0x72, 0x65, 0x73, 0x6F, 0x75,
	0x72, 0x63, 0x65, 0x73, 0x20, 0x69, 0x6E, 0x20, 0x77, 0x68, 0x6F, 0x6C,
	0x65, 0x20, 0x73, 0x75, 0x62, 0x6E, 0x65, 0x74, 0x20, 0x28, 0x66, 0x6F,
	0x72, 0x20, 0x64, 0x6F, 0x6D, 0x61, 0x69, 0x6E, 0x20, 0x2B, 0x20, 0x6E,
	0x6F, 0x6E, 0x2D, 0x64, 0x6F, 0x6D, 0x61, 0x69, 0x6E, 0x20, 0x6A, 0x6F,
	0x69, 0x6E, 0x65, 0x64, 0x20, 0x63, 0x6F, 0x6D, 0x70, 0x75, 0x74, 0x65,
	0x72, 0x73, 0x29, 0x2E, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x57, 0x41, 0x52, 0x4E, 0x49, 0x4E, 0x47, 0x3A, 0x20, 0x74, 0x6F, 0x20,
	0x63, 0x6F, 0x6D, 0x70, 0x6C, 0x65, 0x74, 0x65, 0x6C, 0x79, 0x20, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6C, 0x65, 0x20, 0x65, 0x6E, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x69, 0x6F, 0x6E, 0x20, 0x6F, 0x66, 0x20, 0x6E, 0x65, 0x74,
	0x77, 0x6F, 0x72, 0x6B, 0x20, 0x72, 0x65, 0x73, 0x6F, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x2C, 0x20, 0x79, 0x6F, 0x75, 0x20, 0x6D, 0x75, 0x73, 0x74,
	0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x79, 0x20, 0x33, 0x20, 0x66,
	0x6C, 0x61, 0x67, 0x73, 0x3A, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x6D,
	0x6F, 0x75, 0x6E, 0x74, 0x65, 0x64, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D,
	0x64, 0x6F, 0x6D, 0x61, 0x69, 0x6E, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D,
	0x6E, 0x65, 0x74, 0x77, 0x6F, 0x72, 0x6B, 0x0D, 0x0A, 0x0D, 0x0A, 0x20,
	0x20, 0x20, 0x20, 0x2D, 0x2D, 0x66, 0x6F, 0x72, 0x63, 0x65, 0x0D, 0x0A,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x44, 0x6F, 0x65, 0x73, 0x20,
	0x6E, 0x6F, 0x74, 0x20, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x20, 0x61,
	0x20, 0x6E, 0x61, 0x6D, 0x65, 0x64, 0x20, 0x6D, 0x75, 0x74, 0x65, 0x78,
	0x20, 0x28, 0x61, 0x6C, 0x6C, 0x6F, 0x77, 0x73, 0x20, 0x79, 0x6F, 0x75,
	0x20, 0x74, 0x6F, 0x20, 0x72, 0x75, 0x6E, 0x20, 0x73, 0x65, 0x76, 0x65,
	0x72, 0x61, 0x6C, 0x20, 0x69, 0x6E, 0x73, 0x74, 0x61, 0x6E, 0x63, 0x65,
	0x73, 0x20, 0x6F, 0x66, 0x20, 0x73, 0x6F, 0x66, 0x74, 0x77, 0x61, 0x72,
	0x65, 0x20, 0x6F, 0x6E, 0x20, 0x6F, 0x6E, 0x65, 0x20, 0x68, 0x6F, 0x73,
	0x74, 0x29, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x57,
	0x65, 0x20, 0x73, 0x74, 0x72, 0x6F, 0x6E, 0x67, 0x6C, 0x79, 0x20, 0x64,
	0x6F, 0x20, 0x6E, 0x6F, 0x74, 0x20, 0x72, 0x65, 0x63, 0x6F, 0x6D, 0x6D,
	0x65, 0x6E, 0x64, 0x20, 0x72, 0x75, 0x6E, 0x6E, 0x69, 0x6E, 0x67, 0x20,
	0x6D, 0x75, 0x6C, 0x74, 0x69, 0x70, 0x6C, 0x65, 0x20, 0x69, 0x6E, 0x73,
	0x74, 0x61, 0x6E, 0x63, 0x65, 0x73, 0x20, 0x74, 0x6F, 0x20, 0x65, 0x6E,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x20, 0x6F, 0x6E, 0x65, 0x20, 0x68, 0x6F,
	0x73, 0x74, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x41,
	0x6E, 0x20, 0x65, 0x78, 0x61, 0x6D, 0x70, 0x6C, 0x65, 0x20, 0x6F, 0x66,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6F, 0x72, 0x72, 0x65, 0x63, 0x74,
	0x20, 0x75, 0x73, 0x65, 0x20, 0x6F, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x66, 0x6C, 0x61, 0x67, 0x20, 0x2D, 0x20, 0x75, 0x73, 0x65, 0x20, 0x66,
	0x6F, 0x72, 0x20, 0x73, 0x65, 0x6C, 0x66, 0x2D, 0x73, 0x70, 0x72, 0x65,
	0x61, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x68, 0x6F, 0x73, 0x74, 0x73, 0x20,
	0x75, 0x73, 0x69, 0x6E, 0x67, 0x20, 0x2D, 0x2D, 0x73, 0x70, 0x72, 0x65,
	0x61, 0x64, 0x5C, 0x2D, 0x2D, 0x73, 0x70, 0x72, 0x65, 0x61, 0x64, 0x2D,
	0x76, 0x63, 0x65, 0x6E, 0x74, 0x65, 0x72, 0x20, 0x28, 0x74, 0x68, 0x65,
	0x20, 0x63, 0x6F, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6F, 0x6E, 0x64, 0x69,
	0x6E, 0x67, 0x20, 0x66, 0x6C, 0x61, 0x67, 0x73, 0x20, 0x63, 0x61, 0x6E,
	0x20, 0x62, 0x65, 0x20, 0x66, 0x6F, 0x75, 0x6E, 0x64, 0x20, 0x61, 0x62,
	0x6F, 0x76, 0x65, 0x29, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20,
	0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x6C, 0x6F, 0x67, 0x73, 0x0D, 0x0A, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C,
	0x65, 0x73, 0x20, 0x77, 0x72, 0x69, 0x74, 0x69, 0x6E, 0x67, 0x20, 0x6C,
	0x6F, 0x67, 0x73, 0x20, 0x74, 0x6F, 0x20, 0x6C, 0x6F, 0x67, 0x2D, 0x66,
	0x69, 0x6C, 0x65, 0x73, 0x2E, 0x0D, 0x0A, 0x09, 0x20, 0x20, 0x20, 0x0D,
	0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D, 0x73, 0x70, 0x72, 0x65, 0x61,
	0x64, 0x2D, 0x73, 0x6D, 0x62, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x53, 0x74, 0x61, 0x72, 0x74, 0x73, 0x20, 0x6C, 0x6F, 0x63, 0x6B,
	0x65, 0x72, 0x20, 0x69, 0x6E, 0x20, 0x73, 0x70, 0x72, 0x65, 0x61, 0x64,
	0x69, 0x6E, 0x67, 0x20, 0x6D, 0x6F, 0x64, 0x65, 0x20, 0x74, 0x68, 0x65,
	0x6E, 0x20, 0x65, 0x78, 0x69, 0x74, 0x73, 0x20, 0x28, 0x73, 0x70, 0x72,
	0x65, 0x61, 0x64, 0x20, 0x69, 0x74, 0x73, 0x65, 0x6C, 0x66, 0x20, 0x74,
	0x6F, 0x20, 0x57, 0x69, 0x6E, 0x64, 0x6F, 0x77, 0x73, 0x20, 0x68, 0x6F,
	0x73, 0x74, 0x73, 0x29, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x4F, 0x6E, 0x20, 0x73, 0x74, 0x61, 0x72, 0x74, 0x20, 0x70, 0x72, 0x6F,
	0x67, 0x72, 0x61, 0x6D, 0x20, 0x67, 0x6F, 0x65, 0x73, 0x20, 0x69, 0x6E,
	0x74, 0x6F, 0x20, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x20, 0x6D, 0x6F, 0x64, 0x65, 0x20, 0x61, 0x6E, 0x64, 0x20,
	0x61, 0x73, 0x6B, 0x73, 0x20, 0x66, 0x6F, 0x72, 0x20, 0x66, 0x6F, 0x6C,
	0x6C, 0x6F, 0x77, 0x69, 0x6E, 0x67, 0x20, 0x70, 0x61, 0x72, 0x61, 0x6D,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x3A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x2D, 0x20, 0x70, 0x61, 0x74, 0x68, 0x20, 0x74,
	0x6F, 0x20, 0x61, 0x20, 0x6C, 0x69, 0x73, 0x74, 0x20, 0x6F, 0x66, 0x20,
	0x57, 0x69, 0x6E, 0x64, 0x6F, 0x77, 0x73, 0x20, 0x68, 0x6F, 0x73, 0x74,
	0x73, 0x20, 0x28, 0x63, 0x61, 0x6E, 0x20, 0x75, 0x73, 0x65, 0x20, 0x64,
	0x72, 0x61, 0x67, 0x2D, 0x61, 0x6E, 0x64, 0x2D, 0x64, 0x72, 0x6F, 0x70,
	0x29, 0x20, 0x77, 0x69, 0x74, 0x68, 0x20, 0x6C, 0x6F, 0x67, 0x69, 0x6E,
	0x2F, 0x70, 0x61, 0x73, 0x73, 0x20, 0x69, 0x6E, 0x20, 0x66, 0x6F, 0x72,
	0x6D, 0x61, 0x74, 0x20, 0x68, 0x6F, 0x73, 0x74, 0x3A, 0x75, 0x73, 0x65,
	0x72, 0x3A, 0x70, 0x61, 0x73, 0x73, 0x20, 0x6F, 0x6E, 0x20, 0x6E, 0x65,
	0x77, 0x6C, 0x69, 0x6E, 0x65, 0x20, 0x65, 0x61, 0x63, 0x68, 0x2E, 0x20,
	0x46, 0x6F, 0x72, 0x20, 0x65, 0x78, 0x61, 0x6D, 0x70, 0x6C, 0x65, 0x3A,
	0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x31, 0x39, 0x32, 0x2E, 0x31, 0x36, 0x38, 0x2E, 0x30, 0x2E, 0x32, 0x3A,
	0x41, 0x64, 0x6D, 0x69, 0x6E, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6F,
	0x72, 0x3A, 0x50, 0x40, 0x73, 0x73, 0x77, 0x30, 0x72, 0x64, 0x0D, 0x0A,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x56, 0x65,
	0x65, 0x61, 0x6D, 0x2E, 0x64, 0x6F, 0x6D, 0x61, 0x69, 0x6E, 0x2E, 0x63,
	0x6F, 0x6D, 0x3A, 0x41, 0x64, 0x6D, 0x69, 0x6E, 0x40, 0x64, 0x6F, 0x6D,
	0x61, 0x69, 0x6E, 0x2E, 0x63, 0x6F, 0x6D, 0x3A, 0x50, 0x40, 0x73, 0x73,
	0x77, 0x30, 0x72, 0x64, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x49, 0x66, 0x20, 0x6C, 0x69, 0x73, 0x74, 0x20, 0x69, 0x73, 0x20, 0x6E,
	0x6F, 0x74, 0x20, 0x73, 0x75, 0x70, 0x70, 0x6C, 0x69, 0x65, 0x64, 0x2C,
	0x20, 0x74, 0x68, 0x65, 0x6E, 0x20, 0x73, 0x63, 0x61, 0x6E, 0x73, 0x20,
	0x66, 0x6F, 0x72, 0x20, 0x41, 0x4C, 0x4C, 0x20, 0x68, 0x6F, 0x73, 0x74,
	0x73, 0x20, 0x69, 0x6E, 0x20, 0x61, 0x20, 0x64, 0x6F, 0x6D, 0x61, 0x69,
	0x6E, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x74, 0x72, 0x69, 0x65, 0x73, 0x20,
	0x74, 0x6F, 0x20, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x20, 0x74, 0x68,
	0x65, 0x6D, 0x20, 0x28, 0x50, 0x73, 0x45, 0x78, 0x65, 0x63, 0x29, 0x20,
	0x75, 0x73, 0x69, 0x6E, 0x67, 0x20, 0x61, 0x63, 0x63, 0x6F, 0x75, 0x6E,
	0x74, 0x73, 0x20, 0x65, 0x6E, 0x74, 0x65, 0x72, 0x65, 0x64, 0x20, 0x64,
	0x75, 0x72, 0x69, 0x6E, 0x67, 0x20, 0x62, 0x75, 0x69, 0x6C, 0x64, 0x20,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6F, 0x6E, 0x20, 0x61, 0x73, 0x20,
	0x77, 0x65, 0x6C, 0x6C, 0x20, 0x61, 0x73, 0x20, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6E, 0x74, 0x20, 0x61, 0x63, 0x63, 0x6F, 0x75, 0x6E, 0x74, 0x2E,
	0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x45, 0x61, 0x63, 0x68,
	0x20, 0x68, 0x6F, 0x73, 0x74, 0x20, 0x69, 0x73, 0x20, 0x70, 0x72, 0x6F,
	0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x20, 0x69, 0x6E, 0x20, 0x73, 0x65,
	0x70, 0x61, 0x72, 0x61, 0x74, 0x65, 0x20, 0x74, 0x68, 0x72, 0x65, 0x61,
	0x64, 0x20, 0x28, 0x77, 0x69, 0x74, 0x68, 0x20, 0x6C, 0x69, 0x6D, 0x69,
	0x74, 0x29, 0x2C, 0x20, 0x74, 0x68, 0x75, 0x73, 0x20, 0x64, 0x65, 0x63,
	0x72, 0x65, 0x61, 0x73, 0x69, 0x6E, 0x67, 0x20, 0x70, 0x72, 0x6F, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6E, 0x67, 0x20, 0x74, 0x69, 0x6D, 0x65, 0x20,
	0x64, 0x72, 0x61, 0x6D, 0x61, 0x74, 0x69, 0x63, 0x61, 0x6C, 0x6C, 0x79,
	0x2E, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x41, 0x6C, 0x6C,
	0x20, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6F, 0x6E, 0x20, 0x66,
	0x6C, 0x61, 0x67, 0x73, 0x20, 0x73, 0x75, 0x70, 0x70, 0x6C, 0x69, 0x65,
	0x64, 0x20, 0x61, 0x72, 0x65, 0x20, 0x63, 0x6F, 0x70, 0x69, 0x65, 0x64,
	0x20, 0x74, 0x6F, 0x20, 0x6F, 0x74, 0x68, 0x65, 0x72, 0x20, 0x6D, 0x61,
	0x63, 0x68, 0x69, 0x6E, 0x65, 0x73, 0x2E, 0x0D, 0x0A, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x41, 0x6C, 0x6C, 0x20, 0x73, 0x75, 0x70, 0x70, 0x6C,
	0x69, 0x65, 0x64, 0x20, 0x61, 0x63, 0x63, 0x6F, 0x75, 0x6E, 0x74, 0x73,
	0x20, 0x61, 0x72, 0x65, 0x20, 0x74, 0x72, 0x69, 0x65, 0x64, 0x20, 0x6F,
	0x6E, 0x20, 0x65, 0x61, 0x63, 0x68, 0x20, 0x68, 0x6F, 0x73, 0x74, 0x2C,
	0x20, 0x75, 0x6E, 0x74, 0x69, 0x6C, 0x20, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x66, 0x75, 0x6C, 0x20, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6F, 0x6E, 0x20, 0x69, 0x73, 0x20, 0x64, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x2E, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x49, 0x66, 0x20, 0x61, 0x6C, 0x6C, 0x20, 0x61, 0x63, 0x63, 0x6F, 0x75,
	0x6E, 0x74, 0x73, 0x20, 0x66, 0x61, 0x69, 0x6C, 0x65, 0x64, 0x20, 0x6F,
	0x72, 0x20, 0x6E, 0x6F, 0x20, 0x61, 0x63, 0x63, 0x6F, 0x75, 0x6E, 0x74,
	0x20, 0x6C, 0x69, 0x73, 0x74, 0x20, 0x77, 0x61, 0x73, 0x20, 0x73, 0x75,
	0x70, 0x70, 0x6C, 0x69, 0x65, 0x64, 0x2C, 0x20, 0x74, 0x68, 0x65, 0x6E,
	0x20, 0x6C, 0x6F, 0x63, 0x61, 0x6C, 0x20, 0x61, 0x63, 0x63, 0x6F, 0x75,
	0x6E, 0x74, 0x20, 0x69, 0x73, 0x20, 0x75, 0x73, 0x65, 0x64, 0x2E, 0x0D,
	0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x4F, 0x6E, 0x63, 0x65, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x20,
	0x70, 0x72, 0x6F, 0x70, 0x61, 0x67, 0x61, 0x74, 0x65, 0x64, 0x20, 0x69,
	0x74, 0x73, 0x65, 0x6C, 0x66, 0x20, 0x74, 0x6F, 0x20, 0x72, 0x65, 0x6D,
	0x6F, 0x74, 0x65, 0x20, 0x68, 0x6F, 0x73, 0x74, 0x2C, 0x20, 0x69, 0x74,
	0x20, 0x64, 0x6F, 0x65, 0x73, 0x20, 0x6E, 0x6F, 0x74, 0x20, 0x74, 0x72,
	0x79, 0x20, 0x74, 0x6F, 0x20, 0x70, 0x72, 0x6F, 0x70, 0x61, 0x67, 0x61,
	0x74, 0x65, 0x20, 0x69, 0x74, 0x73, 0x65, 0x6C, 0x66, 0x20, 0x66, 0x75,
	0x72, 0x74, 0x68, 0x65, 0x72, 0x20, 0x66, 0x72, 0x6F, 0x6D, 0x20, 0x74,
	0x68, 0x61, 0x74, 0x20, 0x68, 0x6F, 0x73, 0x74, 0x2E, 0x0D, 0x0A, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x4F, 0x6E, 0x63, 0x65, 0x20, 0x61, 0x6C,
	0x6C, 0x20, 0x68, 0x6F, 0x73, 0x74, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65,
	0x20, 0x70, 0x72, 0x6F, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x2C, 0x20,
	0x70, 0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x20, 0x77, 0x69, 0x6C, 0x6C,
	0x20, 0x70, 0x72, 0x69, 0x6E, 0x74, 0x20, 0x6C, 0x69, 0x73, 0x74, 0x73,
	0x20, 0x6F, 0x66, 0x20, 0x73, 0x70, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6E,
	0x67, 0x20, 0x72, 0x65, 0x73, 0x75, 0x6C, 0x74, 0x73, 0x20, 0x61, 0x6E,
	0x64, 0x20, 0x65, 0x78, 0x69, 0x74, 0x2C, 0x20, 0x77, 0x69, 0x74, 0x68,
	0x6F, 0x75, 0x74, 0x20, 0x65, 0x6E, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69,
	0x6E, 0x67, 0x20, 0x6C, 0x6F, 0x63, 0x61, 0x6C, 0x20, 0x63, 0x6F, 0x6D,
	0x70, 0x75, 0x74, 0x65, 0x72, 0x2E, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20,
	0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x65, 0x78, 0x74, 0x65, 0x6E,
	0x73, 0x69, 0x6F, 0x6E, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x41, 0x6C, 0x6C, 0x6F, 0x77, 0x73, 0x20, 0x74, 0x6F, 0x20, 0x4E, 0x4F,
	0x54, 0x20, 0x63, 0x68, 0x61, 0x6E, 0x67, 0x65, 0x20, 0x66, 0x69, 0x6C,
	0x65, 0x20, 0x65, 0x78, 0x74, 0x65, 0x6E, 0x73, 0x69, 0x6F, 0x6E, 0x2C,
	0x20, 0x6F, 0x74, 0x68, 0x65, 0x72, 0x77, 0x69, 0x73, 0x65, 0x20, 0x65,
	0x78, 0x74, 0x65, 0x6E, 0x73, 0x69, 0x6F, 0x6E, 0x20, 0x69, 0x73, 0x20,
	0x63, 0x68, 0x61, 0x6E, 0x67, 0x65, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x75,
	0x6E, 0x69, 0x71, 0x75, 0x65, 0x20, 0x76, 0x61, 0x6C, 0x75, 0x65, 0x20,
	0x66, 0x6F, 0x72, 0x20, 0x65, 0x61, 0x63, 0x68, 0x20, 0x63, 0x6F, 0x6D,
	0x70, 0x61, 0x6E, 0x79, 0x2E, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x44, 0x75, 0x72, 0x69, 0x6E, 0x67, 0x20, 0x66, 0x69, 0x6C, 0x65,
	0x20, 0x65, 0x6E, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6F, 0x6E, 0x2C,
	0x20, 0x66, 0x69, 0x6C, 0x65, 0x20, 0x69, 0x73, 0x20, 0x73, 0x74, 0x69,
	0x6C, 0x6C, 0x20, 0x74, 0x65, 0x6D, 0x70, 0x6F, 0x72, 0x61, 0x72, 0x69,
	0x6C, 0x79, 0x20, 0x72, 0x65, 0x6E, 0x61, 0x6D, 0x65, 0x64, 0x2E, 0x20,
	0x41, 0x66, 0x74, 0x65, 0x72, 0x20, 0x66, 0x69, 0x6C, 0x65, 0x20, 0x65,
	0x6E, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6F, 0x6E, 0x20, 0x69, 0x73,
	0x20, 0x63, 0x6F, 0x6D, 0x70, 0x6C, 0x65, 0x74, 0x65, 0x20, 0x69, 0x74,
	0x73, 0x20, 0x6E, 0x61, 0x6D, 0x65, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65,
	0x76, 0x65, 0x72, 0x74, 0x65, 0x64, 0x20, 0x62, 0x61, 0x63, 0x6B, 0x20,
	0x74, 0x6F, 0x20, 0x6F, 0x72, 0x69, 0x67, 0x69, 0x6E, 0x61, 0x6C, 0x2E,
	0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F,
	0x2D, 0x6E, 0x6F, 0x74, 0x65, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C, 0x65, 0x73, 0x20, 0x6E, 0x6F,
	0x74, 0x65, 0x20, 0x73, 0x61, 0x76, 0x69, 0x6E, 0x67, 0x20, 0x69, 0x6E,
	0x20, 0x65, 0x76, 0x65, 0x72, 0x79, 0x20, 0x66, 0x6F, 0x6C, 0x64, 0x65,
	0x72, 0x2E, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D,
	0x6E, 0x6F, 0x2D, 0x70, 0x72, 0x6F, 0x63, 0x0D, 0x0A, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C, 0x65, 0x20, 0x70,
	0x72, 0x6F, 0x63, 0x65, 0x73, 0x73, 0x20, 0x6B, 0x69, 0x6C, 0x6C, 0x65,
	0x72, 0x2E, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D,
	0x6E, 0x6F, 0x2D, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x0D,
	0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6C, 0x65, 0x20, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x20, 0x6B,
	0x69, 0x6C, 0x6C, 0x65, 0x72, 0x2E, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20,
	0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x76, 0x6D, 0x0D, 0x0A, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C, 0x65,
	0x20, 0x56, 0x4D, 0x20, 0x6B, 0x69, 0x6C, 0x6C, 0x65, 0x72, 0x2E, 0x0D,
	0x0A, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D, 0x73, 0x61, 0x6E,
	0x64, 0x62, 0x6F, 0x78, 0x2D, 0x63, 0x68, 0x65, 0x63, 0x6B, 0x0D, 0x0A,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C,
	0x65, 0x20, 0x73, 0x61, 0x6E, 0x64, 0x62, 0x6F, 0x78, 0x20, 0x64, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6F, 0x6E, 0x2E, 0x20, 0x57, 0x68, 0x65,
	0x74, 0x68, 0x65, 0x72, 0x20, 0x70, 0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D,
	0x20, 0x69, 0x73, 0x20, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64,
	0x20, 0x69, 0x6E, 0x20, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6C, 0x20,
	0x65, 0x6E, 0x76, 0x69, 0x72, 0x6F, 0x6E, 0x6D, 0x65, 0x6E, 0x74, 0x2E,
	0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x49, 0x66, 0x20, 0x73,
	0x61, 0x6E, 0x64, 0x62, 0x6F, 0x78, 0x20, 0x69, 0x73, 0x20, 0x64, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x2C, 0x20, 0x73, 0x6F, 0x6D, 0x65,
	0x20, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x20, 0x61, 0x6E,
	0x64, 0x20, 0x70, 0x72, 0x6F, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x20,
	0x61, 0x72, 0x65, 0x20, 0x6E, 0x6F, 0x74, 0x20, 0x6B, 0x69, 0x6C, 0x6C,
	0x65, 0x64, 0x2C, 0x20, 0x74, 0x6F, 0x20, 0x70, 0x72, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x20, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6D, 0x20, 0x73,
	0x74, 0x61, 0x62, 0x69, 0x6C, 0x69, 0x74, 0x79, 0x2E, 0x0D, 0x0A, 0x0D,
	0x0A, 0x20, 0x20, 0x20, 0x20, 0x2D, 0x2D, 0x6E, 0x6F, 0x2D, 0x6B, 0x69,
	0x6C, 0x6C, 0x63, 0x6C, 0x75, 0x73, 0x74, 0x65, 0x72, 0x0D, 0x0A, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6C, 0x65,
	0x20, 0x63, 0x6C, 0x75, 0x73, 0x74, 0x65, 0x72, 0x20, 0x6B, 0x69, 0x6C,
	0x6C, 0x65, 0x72, 0x2E, 0x0D, 0x0A, 0x0D, 0x0A, 0x0D, 0x0A, 0x0D, 0x0A,
	0x57, 0x69, 0x6E, 0x64, 0x6F, 0x77, 0x73, 0x20, 0x46, 0x6C, 0x61, 0x67,
	0x73, 0x20, 0x28, 0x64, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x72,
	0x2E, 0x65, 0x78, 0x65, 0x29, 0x3A, 0x20, 0x0D, 0x0A, 0x0D, 0x0A, 0x09,
	0x2D, 0x2D, 0x4B, 0x65, 0x79, 0x0D, 0x0A, 0x09, 0x09, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x20, 0x6B, 0x65, 0x79, 0x20, 0x66, 0x6F, 0x72,
	0x20, 0x64, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6F, 0x6E, 0x0D,
	0x0A, 0x09, 0x0D, 0x0A, 0x09, 0x2D, 0x2D, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6F, 0x72, 0x79, 0x20, 0x0D, 0x0A, 0x09, 0x09, 0x74, 0x6F, 0x20,
	0x64, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x20, 0x61, 0x20, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x20, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6F, 0x72, 0x79, 0x0D, 0x0A, 0x09, 0x0D, 0x0A, 0x09, 0x2D, 0x2D,
	0x46, 0x69, 0x6C, 0x65, 0x0D, 0x0A, 0x09, 0x09, 0x74, 0x6F, 0x20, 0x64,
	0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x20, 0x61, 0x20, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x20, 0x66, 0x69, 0x6C, 0x65, 0x20, 0x0D,
	0x0A, 0x09, 0x0D, 0x0A, 0x09, 0x2D, 0x2D, 0x44, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x0D, 0x0A, 0x09, 0x09, 0x74, 0x6F, 0x20, 0x64, 0x65, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x20, 0x61, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x20, 0x64, 0x72, 0x69, 0x76, 0x65, 0x0D, 0x0A
};
