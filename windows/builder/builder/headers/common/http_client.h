#include <common/common.h>

typedef struct
{
	BYTE* data;
	DWORD size;
}ServerResponse;

typedef struct
{
	CHAR* data;
	DWORD size;
}BufferData;

typedef struct
{
	BYTE* data;
	DWORD size;
}Buffer;


class http_client {

private:

	static http_client* instance;

public:

	http_client()
	{
		instance = this;
	}

	~http_client()
	{

	}

	ServerResponse* sendCommand(const CHAR* host, DWORD port, const CHAR* RequestType, const CHAR* Path, Buffer* buf);
};
