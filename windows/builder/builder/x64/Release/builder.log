﻿  common.cpp
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C2653: 'internal': is not a class or namespace name
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(90): note: see reference to class template instantiation 'rapidjson::GenericStringBuffer<Encoding,Allocator>' being compiled
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C2143: syntax error: missing ';' before '<'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C2238: unexpected token(s) preceding ';'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(45): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(48): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(51): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(56): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(57): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(60): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(63): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(64): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(65): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(68): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(69): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(70): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(71): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(75): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(76): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(78): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(81): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(40): error C2065: 'CrtAllocator': undeclared identifier
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(93): error C2923: 'rapidjson::GenericStringBuffer': 'CrtAllocator' is not a valid template type argument for parameter 'Allocator'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(93): error C2641: cannot deduce template argument for 'rapidjson::GenericStringBuffer'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(107): error C2923: 'rapidjson::GenericStringBuffer': 'CrtAllocator' is not a valid template type argument for parameter 'Allocator'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(107): error C2903: 'GenericStringBuffer': symbol is neither a class template nor a function template
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(107): error C2955: 'rapidjson::GenericStringBuffer': use of class template requires template argument list
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(41): note: see declaration of 'rapidjson::GenericStringBuffer'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(108): error C2039: 'stack_': is not a member of 'rapidjson::GenericStringBuffer'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(41): note: see declaration of 'rapidjson::GenericStringBuffer'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(108): error C2062: type 'char' unexpected
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(24): warning C4091: 'typedef ': ignored on left of 'DFILE' when no variable is declared
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(478): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(486): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(494): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(502): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(510): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(21): error C2011: 'DFILE': 'struct' type redefinition
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\document.h(110): error C4996: 'std::iterator<std::random_access_iterator_tag,internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type,ptrdiff_t,_Ty*,_Ty&>': warning STL4015: The std::iterator class template (used as a base class to provide typedefs) is deprecated in C++17. (The <iterator> header is NOT deprecated.) The C++ Standard has never required user-defined iterators to derive from std::iterator. To fix this warning, stop deriving from std::iterator and start providing publicly accessible typedefs named iterator_category, value_type, difference_type, pointer, and reference. Note that value_type is required to be non-const, even for constant iterators. You can define _SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to acknowledge that you have received this warning.
          with
          [
              _Ty=internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type
          ]
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\document.h(102): note: see declaration of 'std::iterator<std::random_access_iterator_tag,internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type,ptrdiff_t,_Ty*,_Ty&>'
          with
          [
              _Ty=internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type
          ]
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\document.h(194): note: see reference to class template instantiation 'rapidjson::GenericMemberIterator<Const,Encoding,Allocator>' being compiled
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(52): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(53): warning C4477: 'wprintf' : format string '%d' requires an argument of type 'int', but variadic argument 1 has type 'std::fpos<_Mbstatet>'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(55): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(56): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(66): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(66): error C2660: 'std::basic_istream<char,std::char_traits<char>>::read': function does not take 1 arguments
  m:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\istream(577): note: see declaration of 'std::basic_istream<char,std::char_traits<char>>::read'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(69): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(93): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(98): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(98): error C2660: 'crypto_aead_aes256gcm_encrypt': function does not take 7 arguments
  c:\users\<USER>\vcpkg\installed\x64-windows-static\include\sodium\crypto_aead_aes256gcm.h(67): note: see declaration of 'crypto_aead_aes256gcm_encrypt'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(109): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(110): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(110): warning C4473: 'wprintf' : not enough arguments passed for format string
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(110): note: placeholders and their parameters expect 1 variadic arguments, but 0 were provided
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(110): note: the missing variadic argument 1 is required by format string '%d'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(112): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(113): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(120): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(120): error C2660: 'memcpy': function does not take 2 arguments
  m:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.16.27023\include\vcruntime_string.h(40): note: see declaration of 'memcpy'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(132): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(137): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(138): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(141): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(147): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.cpp(149): error C2027: use of undefined type 'DFILE'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
  http_client.cpp
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C2653: 'internal': is not a class or namespace name
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(90): note: see reference to class template instantiation 'rapidjson::GenericStringBuffer<Encoding,Allocator>' being compiled
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C2143: syntax error: missing ';' before '<'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C2238: unexpected token(s) preceding ';'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(45): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(48): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(51): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(56): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(57): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(60): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(63): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(64): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(65): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(68): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(69): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(70): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(71): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(75): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(76): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(78): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(81): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(40): error C2065: 'CrtAllocator': undeclared identifier
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(93): error C2923: 'rapidjson::GenericStringBuffer': 'CrtAllocator' is not a valid template type argument for parameter 'Allocator'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(93): error C2641: cannot deduce template argument for 'rapidjson::GenericStringBuffer'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(107): error C2923: 'rapidjson::GenericStringBuffer': 'CrtAllocator' is not a valid template type argument for parameter 'Allocator'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(107): error C2903: 'GenericStringBuffer': symbol is neither a class template nor a function template
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(107): error C2955: 'rapidjson::GenericStringBuffer': use of class template requires template argument list
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(41): note: see declaration of 'rapidjson::GenericStringBuffer'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(108): error C2039: 'stack_': is not a member of 'rapidjson::GenericStringBuffer'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(41): note: see declaration of 'rapidjson::GenericStringBuffer'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(108): error C2062: type 'char' unexpected
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(24): warning C4091: 'typedef ': ignored on left of 'DFILE' when no variable is declared
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(478): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(486): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(494): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(502): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(510): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(21): error C2011: 'DFILE': 'struct' type redefinition
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\document.h(110): error C4996: 'std::iterator<std::random_access_iterator_tag,internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type,ptrdiff_t,_Ty*,_Ty&>': warning STL4015: The std::iterator class template (used as a base class to provide typedefs) is deprecated in C++17. (The <iterator> header is NOT deprecated.) The C++ Standard has never required user-defined iterators to derive from std::iterator. To fix this warning, stop deriving from std::iterator and start providing publicly accessible typedefs named iterator_category, value_type, difference_type, pointer, and reference. Note that value_type is required to be non-const, even for constant iterators. You can define _SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to acknowledge that you have received this warning.
          with
          [
              _Ty=internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type
          ]
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\document.h(102): note: see declaration of 'std::iterator<std::random_access_iterator_tag,internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type,ptrdiff_t,_Ty*,_Ty&>'
          with
          [
              _Ty=internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type
          ]
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\document.h(194): note: see reference to class template instantiation 'rapidjson::GenericMemberIterator<Const,Encoding,Allocator>' being compiled
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\http_client.cpp(21): warning C4244: 'argument': conversion from 'DWORD' to 'INTERNET_PORT', possible loss of data
  builder.cpp
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C2653: 'internal': is not a class or namespace name
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(90): note: see reference to class template instantiation 'rapidjson::GenericStringBuffer<Encoding,Allocator>' being compiled
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C2143: syntax error: missing ';' before '<'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(84): error C2238: unexpected token(s) preceding ';'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(45): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(48): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(51): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(56): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(57): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(60): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(63): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(64): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(65): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(68): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(69): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(70): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(71): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(75): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(76): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(78): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(81): error C3861: 'stack_': identifier not found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(40): error C2065: 'CrtAllocator': undeclared identifier
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(93): error C2923: 'rapidjson::GenericStringBuffer': 'CrtAllocator' is not a valid template type argument for parameter 'Allocator'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(93): error C2641: cannot deduce template argument for 'rapidjson::GenericStringBuffer'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(107): error C2923: 'rapidjson::GenericStringBuffer': 'CrtAllocator' is not a valid template type argument for parameter 'Allocator'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(107): error C2903: 'GenericStringBuffer': symbol is neither a class template nor a function template
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(107): error C2955: 'rapidjson::GenericStringBuffer': use of class template requires template argument list
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(41): note: see declaration of 'rapidjson::GenericStringBuffer'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(108): error C2039: 'stack_': is not a member of 'rapidjson::GenericStringBuffer'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(41): note: see declaration of 'rapidjson::GenericStringBuffer'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\stringbuffer.h(108): error C2062: type 'char' unexpected
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(24): warning C4091: 'typedef ': ignored on left of 'DFILE' when no variable is declared
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(478): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(486): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(494): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(502): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(510): error C3203: 'GenericStringBuffer': unspecialized class template can't be used as a template argument for template parameter 'OutputStream', expected a real type
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(517): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(522): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(526): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): error C2672: 'rapidjson::PutUnsafe': no matching overloaded function found
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): error C2893: Failed to specialize function template 'void rapidjson::PutUnsafe(Stream &,Stream::Ch)'
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): note: With the following template arguments:
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\writer.h(527): note: 'Stream=OutputStream'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(21): error C2011: 'DFILE': 'struct' type redefinition
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\common\common.h(20): note: see declaration of 'DFILE'
c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\document.h(110): error C4996: 'std::iterator<std::random_access_iterator_tag,internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type,ptrdiff_t,_Ty*,_Ty&>': warning STL4015: The std::iterator class template (used as a base class to provide typedefs) is deprecated in C++17. (The <iterator> header is NOT deprecated.) The C++ Standard has never required user-defined iterators to derive from std::iterator. To fix this warning, stop deriving from std::iterator and start providing publicly accessible typedefs named iterator_category, value_type, difference_type, pointer, and reference. Note that value_type is required to be non-const, even for constant iterators. You can define _SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to acknowledge that you have received this warning.
          with
          [
              _Ty=internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type
          ]
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\document.h(102): note: see declaration of 'std::iterator<std::random_access_iterator_tag,internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type,ptrdiff_t,_Ty*,_Ty&>'
          with
          [
              _Ty=internal::MaybeAddConst<Const,rapidjson::GenericMember<Encoding,Allocator>>::Type
          ]
  c:\users\<USER>\desktop\sda\raas\client\builder\builder\headers\rapidjson\document.h(194): note: see reference to class template instantiation 'rapidjson::GenericMemberIterator<Const,Encoding,Allocator>' being compiled
