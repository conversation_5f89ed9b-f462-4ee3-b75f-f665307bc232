#include "common.h"

typedef struct ipnode
{
	CHAR  CurrentIp[16];
	CHAR  mainIp[16];
	CHAR  ip_list[MAX_PATH][MAX_PATH];
};

typedef struct netparam
{
	INT EntryIpIndex;
	INT IdentifierIndex;
	CHAR temp_host[16];
};


class net {

private:




public:
	static net* instance;
	INT    ipnode_count = 0;
	INT    ipIdentifierIndex = 0;
	ipnode IP_NODE[MAX_PATH];

	volatile BOOL netEnum = FALSE;
	BOOL EnumHosts();
	static DWORD WINAPI ValidateSmbHosts(LPVOID lparam);
	static DWORD WINAPI GetSharedFolder(LPVOID lparam);
	//WCHAR SHARE_PATH[MAX_PATH];



	net()
	{
		instance = this;
		WSADATA wsData;
		WSAStartup(MAKEWORD(2, 2), &wsData);

		//IP_NODE[0] = new ip_list();
		//for(int i = 0; i < MAX_PATH;i++)
		//{
		//	IP_NODE[i] = NULL;
		//}
	}

	~net()
	{
		instance = nullptr;
		WSACleanup();
	}

};
