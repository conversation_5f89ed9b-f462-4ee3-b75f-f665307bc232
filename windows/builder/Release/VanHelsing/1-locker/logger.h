// New file: Create a proper logging system
#pragma once
#include "common.h"

enum LogLevel {
    LOG_DEBUG,
    LOG_INFO,
    LOG_WARNING,
    LOG_ERROR,
    LOG_CRITICAL
};

class Logger {
private:
    static Logger* instance;
    std::mutex logMutex;
    LogLevel minLevel;
    std::wofstream logFile;
    bool consoleOutput;
    
public:
    Logger(LogLevel level = LOG_INFO);
    ~Logger();
    
    static Logger* GetInstance();
    
    void SetLogLevel(LogLevel level);
    void EnableConsoleOutput(bool enable);
    bool OpenLogFile(const std::wstring& path);
    
    void Log(LogLevel level, const wchar_t* format, ...);
    
    // Convenience methods
    void Debug(const wchar_t* format, ...);
    void Info(const wchar_t* format, ...);
    void Warning(const wchar_t* format, ...);
    void Error(const wchar_t* format, ...);
    void Critical(const wchar_t* format, ...);
};

// Global shorthand
#define LOG_DEBUG(...)    Logger::GetInstance()->Debug(__VA_ARGS__)
#define LOG_INFO(...)     Logger::GetInstance()->Info(__VA_ARGS__)
#define LOG_WARNING(...)  Logger::GetInstance()->Warning(__VA_ARGS__)
#define LOG_ERROR(...)    Logger::GetInstance()->Error(__VA_ARGS__)
#define LOG_CRITICAL(...) Logger::GetInstance()->Critical(__VA_ARGS__)