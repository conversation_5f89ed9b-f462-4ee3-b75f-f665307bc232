#include  "common.h"

typedef struct encPad {
	char Hexkey[crypto_aead_chacha20poly1305_KEYBYTES * 2 + 1];
	char Hexnonce[crypto_aead_chacha20poly1305_NPUBBYTES * 2 + 1];
};

class decryption {

private:
	static decryption* instance;
public:
	decryption()
	{
		instance = this;
	}

	//encPad* GenChcahaKey();
	void decryptFile(const WCHAR* file_path, const unsigned char* publicKey, const unsigned char* privateKey);


};