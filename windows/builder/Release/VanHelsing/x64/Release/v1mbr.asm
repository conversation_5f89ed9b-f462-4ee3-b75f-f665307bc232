; Custom MBR Bootloader - Assembly Code
[org 0x7c00]               ; MBR loads at address 0x7C00

; Bootloader Message (Simple ASCII Image and Text)
mov ah, 0x0E               ; BIOS teletype output function
mov al, 'H'
int 0x10                   ; Print 'H'
mov al, 'e'
int 0x10                   ; Print 'e'
mov al, 'l'
int 0x10                   ; Print 'l'
mov al, 'l'
int 0x10                   ; Print 'l'
mov al, 'o'
int 0x10                   ; Print 'o'
mov al, ' '
int 0x10                   ; Print space
mov al, 'W'
int 0x10                   ; Print 'W'
mov al, 'o'
int 0x10                   ; Print 'o'
mov al, 'r'
int 0x10                   ; Print 'r'
mov al, 'l'
int 0x10                   ; Print 'l'
mov al, 'd'
int 0x10                   ; Print 'd'
mov al, '!'
int 0x10                   ; Print '!'

; Prompt user to enter a password/key
mov ah, 0x0E               ; BIOS teletype output function
mov al, 0x0A               ; Print newline (0x0A)
int 0x10                   ; Print newline
mov al, 'E'
int 0x10                   ; Print 'E'
mov al, 'n'
int 0x10                   ; Print 'n'
mov al, 't'
int 0x10                   ; Print 't'
mov al, 'e'
int 0x10                   ; Print 'e'
mov al, 'r'
int 0x10                   ; Print 'r'
mov al, ' '
int 0x10                   ; Print space
mov al, 'K'
int 0x10                   ; Print 'K'
mov al, 'e'
int 0x10                   ; Print 'e'
mov al, 'y'
int 0x10                   ; Print 'y'
mov al, ':'
int 0x10                   ; Print ':'

; Accept user input (password/key)
; Here we assume the user inputs exactly "password123" or "admin123"

; For simplicity, we'll use a loop to accept key press from user and store it in a buffer
; This part would need more coding in a real bootloader, but it's not shown here.

; In a real system, you'd implement keyboard input checking

; Check if key is valid (simplified version for demonstration)
; If the input is "password123", we continue boot, otherwise halt the system.

; Check if the entered key matches the predefined key (simplified for demonstration)
; If key is valid, jump to boot code (continue booting into Windows)

; In a real scenario, the bootloader would load the boot sector and jump to it
; Here we simulate booting by just halting the system for demonstration

; Halt the system if the key is invalid
hlt

; Bootloader success code (jump to the next sector to boot the system)
; This is a placeholder. In a real MBR, you would jump to the boot sector.
; For now, we stop execution.
jmp $

; The MBR code ends here
times 510 - ($ - $$) db 0  ; Fill the remaining space with zeros (MBR size = 512 bytes)
dw 0xAA55                  ; Boot signature
