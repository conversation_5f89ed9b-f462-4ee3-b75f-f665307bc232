; minimal_bootloader.asm
BITS 16
ORG 0x7C00

start:
    ; Set up segment registers
    xor ax, ax
    mov ds, ax
    mov es, ax

    ; Clear screen (video mode 03h)
    mov ah, 0x00
    mov al, 0x03
    int 0x10

    ; Set cursor position (row 0, col 0)
    mov ah, 0x02
    mov bh, 0x00
    mov dh, 0
    mov dl, 0
    int 0x10

    ; Display message
    mov si, msg
print_loop:
    lodsb
    cmp al, 0
    je halt
    mov ah, 0x0E
    int 0x10
    jmp print_loop

halt:
    cli
    hlt
    jmp $

msg db "Bootloader works!", 0

times 510 - ($ - $$) db 0
dw 0xAA55
