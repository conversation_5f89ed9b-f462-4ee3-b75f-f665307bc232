#define _CRT_SECURE_NO_WARNINGS
#include <windows.h>
#include <stdio.h>
#include <sodium.h>
#include <string>
#include <sstream>
#include <vector>

//void WriteToConsole(const char* message);
//LPVOID pAlloc(DWORD size);
//LPVOID pSet(LPVOID ptr, INT val, DWORD size);
//LPVOID pCopy(LPVOID dest, LPCVOID src, DWORD size);
//VOID   pFree(LPVOID mem);

#define AES_KEY_LEN  crypto_aead_aes256gcm_KEYBYTES
#define AES_NONCE_LEN crypto_aead_aes256gcm_NPUBBYTES

extern std::string AES_MASTER_KEY;
extern std::string AES_MASTER_NONCE;

extern BOOL Debug;
void splitHashAndNonce(const std::wstring& input, std::string& master_key, std::string& master_nonce);
BOOL IsArgExists(INT argc, LPWSTR* argv, const WCHAR* argname);
BOOL ParseArgs();

#define NtCurrentProcess() ((HANDLE)(LONG_PTR)-1)

typedef long NTSTATUS;
#define NTAPI __stdcall 

#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif


#ifdef _M_X64  // x64 architecture
extern "C" {
    DWORD wNtAllocateVirtualMemory;
    UINT_PTR sysAddrNtAllocateVirtualMemory;
    NTSTATUS NtAllocateVirtualMemory(HANDLE ProcessHandle, PVOID* BaseAddress, ULONG ZeroBits, SIZE_T* RegionSize, ULONG AllocationType, ULONG Protect);
}
#else  // x86 architecture
typedef NTSTATUS (NTAPI* NtAllocateVirtualMemory)(HANDLE ProcessHandle, PVOID* BaseAddress, ULONG ZeroBits, SIZE_T* RegionSize, ULONG AllocationType, ULONG Protect);
extern NtAllocateVirtualMemory FnNtAllocateVirtualMemory;
#endif





























//typedef LPVOID(WINAPI* funcHeapAlloc)(HANDLE hHeap, DWORD dwFlags, SIZE_T dwBytes);
//extern funcHeapAlloc pHeapAlloc;
//
//typedef HANDLE(WINAPI* funcGetProcessHeap)();
//extern funcGetProcessHeap pGetProcessHeap;
//
//typedef BOOL(WINAPI* funcHeapFree)(HANDLE hHeap, DWORD dwFlags, LPVOID lpMem);
//extern funcHeapFree pHeapFree;
//
//
//typedef NTSTATUS(NTAPI* fpRtlGetVersion)(OSVERSIONINFOEXA* lpVersionInformation);
//extern fpRtlGetVersion pRtlGetVersion;
//
//typedef DWORD(WINAPI* funcGetModuleFileNameA)(HMODULE hModule, LPSTR   lpFilename, DWORD   nSize);
//extern funcGetModuleFileNameA pGetModuleFileNameA;
//
//
//typedef BOOL(WINAPI* funcCreateProcessA)(LPCSTR lpApplicationName, LPSTR lpCommandLine, LPSECURITY_ATTRIBUTES lpProcessAttributes, LPSECURITY_ATTRIBUTES lpThreadAttributes, BOOL bInheritHandles, DWORD dwCreationFlags, LPVOID lpEnvironment, LPCSTR lpCurrentDirectory, LPSTARTUPINFOA lpStartupInfo, LPPROCESS_INFORMATION lpProcessInformation);
//extern funcCreateProcessA pCreateProcessA;
//
//typedef DWORD(WINAPI* funcWaitForSingleObject)(HANDLE hHandle, DWORD dwMilliseconds);
//extern funcWaitForSingleObject pWaitForSingleObject;
//
//typedef DWORD(WINAPI* funcGetEnvironmentVariableA)(LPCSTR lpName, LPSTR  lpBuffer, DWORD  nSize);
//extern funcGetEnvironmentVariableA pGetEnvironmentVariableA;
//
//
//typedef UINT(WINAPI* funcGetWindowsDirectoryA)(LPSTR lpBuffer, UINT  uSize);
//extern funcGetWindowsDirectoryA pGetWindowsDirectoryA;
//
//typedef BOOL(WINAPI* funcGetVolumeInformationA)(LPCSTR lpRootPathName, LPSTR lpVolumeNameBuffer, DWORD   nVolumeNameSize, LPDWORD lpVolumeSerialNumber, LPDWORD lpMaximumComponentLength, LPDWORD lpFileSystemFlags, LPSTR lpFileSystemNameBuffer, DWORD nFileSystemNameSize);
//extern funcGetVolumeInformationA pGetVolumeInformationA;
//
//
//
//
//BOOL InitializeLibsFunctions();
