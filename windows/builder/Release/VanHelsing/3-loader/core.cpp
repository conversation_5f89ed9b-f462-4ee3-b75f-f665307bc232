#include "core.h"
#include <codecvt>

#ifdef _M_X64  // x64 architecture
extern "C" {
 
}

#else 
NtAllocateVirtualMemory FnNtAllocateVirtualMemory;

#endif

BOOL Debug = FALSE;
std::string AES_MASTER_KEY;
std::string AES_MASTER_NONCE;

std::string wstring_to_string(const std::wstring& wstr)
{
    std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
    return converter.to_bytes(wstr);
}


void splitHashAndNonce(const std::wstring& input, std::string& master_key, std::string& master_nonce)
{

   std::string str_input  = wstring_to_string(input);

    size_t pos = str_input.find(':');  // Find the position of ':'
    if (pos != std::string::npos) {
        master_key = str_input.substr(0, pos);  // Extract the part before ':'
        master_nonce = str_input.substr(pos + 1);  // Extract the part after ':'
    }
    else 
    {
        wprintf(L"Invalid format: ':' not found in input\n");
    }
}

BOOL IsArgExists(INT argc, LPWSTR* argv, const WCHAR* argname)
{
    for (int i = 0; i < argc; i++)
    {
        if (lstrcmpW(argv[i], argname) == 0)
        {
            return TRUE;
        }
    }
    return FALSE;
}

std::wstring GetArgsValue(INT argc, LPWSTR* argv, const WCHAR* argname)
{
    for (int i = 0; i < argc; i++)
    {
        if (lstrcmpW(argv[i], argname) == 0)
        {
            std::wstring argValue;// = argv[i + 1];
            argValue.append(argv[i + 1]);
            return argValue;
        }
    }
    return NULL;
}

VOID DebugVerbose(WCHAR* print, BOOL IsEnabled)
{
    if (IsEnabled == TRUE)
    {
        wprintf(L"%s\n", print);
    }
}

VOID DebugVerbose(std::wstring print, BOOL IsEnabled)
{
    if (IsEnabled == TRUE)
    {
        wprintf(L"%s\n", print);
    }
}


BOOL ParseArgs()
{
 
    int argc;
    LPWSTR* argv = CommandLineToArgvW(GetCommandLineW(), &argc);

    if (!argv)
    {
        MessageBoxW(NULL, L"Failed to parse command line", L"Error", MB_OK | MB_ICONERROR);
        return FALSE;
    }

  

    if (IsArgExists(argc, argv, L"-v") == TRUE)
    {
        AllocConsole();
        freopen("CONOUT$", "w", stdout);
        freopen("CONOUT$", "w", stderr);
        freopen("CONIN$", "r", stdin);

        Debug = TRUE;
    }

    if (IsArgExists(argc, argv, L"--Password") == TRUE)
    {
        std::wstring password = GetArgsValue(argc, argv, L"--Password");

        if(password.empty())
        {
            DebugVerbose((WCHAR*)L"Empty password\n", TRUE);
            getchar();
            return FALSE;
        }
        else
        {
            splitHashAndNonce(password, AES_MASTER_KEY, AES_MASTER_NONCE);

            //wprintf(L"AES_MASTER_KEY : %s\nAES_MASTER_NONCE: %s \n", AES_MASTER_KEY.c_str(), AES_MASTER_NONCE.c_str());
            //return FALSE;
        }
    }
    else
    {
        DebugVerbose((WCHAR*)L"The password is required\n", TRUE);
        getchar();
        return FALSE;
    }


    return TRUE;
}






















//DWORD wNtProtectVirtualMemory;
//UINT_PTR sysAddrNtProtectVirtualMemory;



//funcHeapAlloc               pHeapAlloc;
//funcGetProcessHeap          pGetProcessHeap;
//funcHeapFree                pHeapFree;
//fpRtlGetVersion             pRtlGetVersion;
//funcGetModuleFileNameA      pGetModuleFileNameA;
//funcCreateProcessA          pCreateProcessA;
//funcWaitForSingleObject     pWaitForSingleObject;
//funcGetEnvironmentVariableA pGetEnvironmentVariableA;
//funcGetWindowsDirectoryA    pGetWindowsDirectoryA;
//funcGetVolumeInformationA   pGetVolumeInformationA;



//BOOL InitializeLibsFunctions()
//{
//    CHAR msg[255];
//
//    HMODULE k32dll = LoadLibraryA("Kernel32.dll");
//    if (k32dll == NULL)
//    {
//        wsprintfA(msg, "Faild to Load k32dll dll %d", GetLastError());
//        WriteToConsole(msg);
//        WriteToConsole("\n");
//        return FALSE;
//    }
//
//    pHeapAlloc = (funcHeapAlloc)GetProcAddress(k32dll, "HeapAlloc");
//    if (pHeapAlloc == NULL)
//    {
//        wsprintfA(msg, "Faild to Load HeapAlloc function %d", GetLastError());
//        WriteToConsole(msg);
//        WriteToConsole("\n");
//        return FALSE;
//    }
//
//    pGetProcessHeap = (funcGetProcessHeap)GetProcAddress(k32dll, "GetProcessHeap");
//    if (pGetProcessHeap == NULL)
//    {
//        wsprintfA(msg, "Faild to Load GetProcessHeap function %d", GetLastError());
//        WriteToConsole(msg);
//        WriteToConsole("\n");
//        return FALSE;
//    }
//
//    pHeapFree = (funcHeapFree)GetProcAddress(k32dll, "HeapFree");
//    if (pHeapFree == NULL)
//    {
//        wsprintfA(msg, "Faild to Load HeapFree function %d", GetLastError());
//        WriteToConsole(msg);
//        WriteToConsole("\n");
//        return FALSE;
//    }
//
//    pGetModuleFileNameA = (funcGetModuleFileNameA)GetProcAddress(k32dll, "GetModuleFileNameA");
//    if (pGetModuleFileNameA == NULL)
//    {
//        wsprintfA(msg, "Faild to Load GetModuleFileNameA function %d", GetLastError());
//        WriteToConsole(msg);
//        WriteToConsole("\n");
//        return FALSE;
//    }
//
//
//    pWaitForSingleObject = (funcWaitForSingleObject)GetProcAddress(k32dll, "WaitForSingleObject");
//    if (pWaitForSingleObject == NULL)
//    {
//        wsprintfA(msg, "Faild to Load WaitForSingleObject function %d", GetLastError());
//        WriteToConsole(msg);
//        WriteToConsole("\n");
//        return FALSE;
//    }
//
//    pGetEnvironmentVariableA = (funcGetEnvironmentVariableA)GetProcAddress(k32dll, "GetEnvironmentVariableA");
//    if (pGetEnvironmentVariableA == NULL)
//    {
//        wsprintfA(msg, "Faild to Load GetEnvironmentVariableA function %d", GetLastError());
//        WriteToConsole(msg);
//        WriteToConsole("\n");
//        return FALSE;
//    }
//
//    pGetWindowsDirectoryA = (funcGetWindowsDirectoryA)GetProcAddress(k32dll, "GetWindowsDirectoryA");
//    if (pGetWindowsDirectoryA == NULL)
//    {
//        wsprintfA(msg, "Faild to Load GetWindowsDirectoryA function %d", GetLastError());
//        WriteToConsole(msg);
//        WriteToConsole("\n");
//        return FALSE;
//    }
//
//    pGetVolumeInformationA = (funcGetVolumeInformationA)GetProcAddress(k32dll, "GetVolumeInformationA");
//    if (pGetVolumeInformationA == NULL)
//    {
//        wsprintfA(msg, "Faild to Load GetVolumeInformationA function %d", GetLastError());
//        WriteToConsole(msg);
//        WriteToConsole("\n");
//        return FALSE;
//    }
//
//
//
//
//
//    return TRUE;
//}


//void WriteToConsole(const char* message)
//{
//    HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
//    DWORD written;
//    WriteConsoleA(hConsole, message, lstrlenA(message), &written, NULL);
//}
//
//LPVOID pSet(LPVOID ptr, int val, DWORD size)
//{
//    if (ptr && size > 0)
//    {
//        volatile byte* p = (byte*)ptr;
//        for (DWORD i = 0; i < size; i++, p++)
//            *p = val;
//    }
//    return ptr;
//}
//
//LPVOID pAlloc(DWORD size)
//{
//    LPVOID mem = pHeapAlloc(pGetProcessHeap(), 0, size);
//    if (!mem) return NULL;
//
//    pSet(mem, 0, size);
//
//    return mem;
//}
//
//LPVOID pCopy(LPVOID dest, LPCVOID src, DWORD size)
//{
//    if (dest && src && size > 0)
//    {
//        byte* to = (byte*)dest;
//        byte* from = (byte*)src;
//        while (size--) *to++ = *from++;
//    }
//    return dest;
//}
//
//
//void pFree(LPVOID mem)
//{
//    if (mem)
//    {
//        pHeapFree(pGetProcessHeap(), 0, mem);
//    }
//}
