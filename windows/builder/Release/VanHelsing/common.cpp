// Use smart pointers instead of raw pointers
CHAR* BSTRToCharArray(BSTR bstr)
{
    if (!bstr)
        return nullptr;

    int size = WideCharToMultiByte(CP_UTF8, 0, bstr, -1, NULL, 0, NULL, NULL);
    if (size == 0)
        return nullptr;

    // Use std::unique_ptr for automatic cleanup
    std::unique_ptr<char[]> result(new char[size]);
    WideCharToMultiByte(CP_UTF8, 0, bstr, -1, result.get(), size, NULL, NULL);

    // Only if you need to return raw pointer (consider changing API)
    return _strdup(result.get());
}

// Caller must free the result with free()