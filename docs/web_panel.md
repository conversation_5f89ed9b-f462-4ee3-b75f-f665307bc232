# Web Panel Documentation

VanHelsing uses two web panels for operation management and victim extortion.

## Admin Panel

The admin panel provides a complete management interface for the ransomware operation.

### Features

- **Dashboard**: Overview of infections, payments, and statistics
- **Target Management**: List of infected systems with details
- **Chat Interface**: Communication with victims
- **Build Management**: Request and download custom builds
- **Payment Tracking**: Monitor Bitcoin payments
- **Affiliate Management**: Track affiliate performance and payouts

### User Roles

- **Administrator**: Full access to all features
- **Developer**: Access to build system and technical features
- **Affiliate**: Limited access to own victims and builds
- **Support**: Access to chat and basic victim information

### Security

- Session-based authentication
- Role-based access control
- Activity logging
- IP-based access restrictions

## Blog Panel (Leak Site)

The public-facing leak site used for double-extortion tactics.

### Features

- **Leak Announcements**: Public disclosure of victim companies
- **Countdown Timers**: Showing time until data publication
- **File Tree Viewer**: Proof of stolen data
- **Data Samples**: Partial data leaks to prove possession
- **Search Functionality**: Find specific leaks or companies

### Technical Implementation

- PHP-based web application
- MySQL database for leak storage
- File tree visualization using JavaScript
- Countdown timers with JavaScript
- Responsive design for mobile/desktop viewing

### Content Management

- Leak posts are managed through the admin panel
- File trees are generated from exfiltrated data
- Publication dates are set with configurable timers
- Data samples are carefully selected for maximum impact