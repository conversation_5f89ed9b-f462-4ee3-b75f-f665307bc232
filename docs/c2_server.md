# C2 Server Documentation

The Command and Control (C2) server coordinates all components of the VanHelsing operation.

## Architecture

- **API Endpoint**: `api.php` handles all client communications
- **Database**: Stores victim data, builds, and encryption keys
- **Web Panels**: Admin interface and public leak site

## API Endpoints

The C2 server exposes these main endpoints:

### Builder Endpoints
- `api.php?action=get_builds_task`: Returns pending build requests
- `api.php?action=upload_builds_task`: Receives completed builds
- `api.php?action=upload_builds_decrypter_task`: Receives decrypter builds

### Victim Communication
- `api.php?action=register_target`: Records new infections
- `api.php?action=send_chat`: Handles chat messages
- `api.php?action=get_chat`: Retrieves chat history

### Payment Processing
- `api.php?action=check_payment`: Verifies Bitcoin payments
- `api.php?action=authorize_decryption`: Enables decryption

## Communication Protocol

- Uses standard HTTP/HTTPS
- JSON-formatted responses
- Session-based authentication for admin access
- Simple GET/POST parameters for client communication

## Server Configuration

- Primary C2 server: `**************:8080`
- Secondary C2 server: `**************:8080`
- Database server: MySQL on localhost
- Bitcoin RPC for payment verification

## Security Measures

- IP filtering for admin access
- Rate limiting to prevent abuse
- Session timeout configuration
- Database connection using prepared statements