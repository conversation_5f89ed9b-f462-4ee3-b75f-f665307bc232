# Builder Documentation

The Windows Builder is responsible for creating customized ransomware payloads.

## Features

- Automated build process for ransomware components
- Customization of encryption keys and victim IDs
- Compilation of locker, decrypter, and loader executables
- Integration with C2 server for build distribution

## Build Process

1. **Task Polling**:
   - Polls C2 server for pending build requests
   - Retrieves build parameters (public key, architecture, etc.)

2. **Compilation**:
   - Sets up build environment in temporary directory
   - Injects custom parameters into source files
   - Compiles executables using Visual Studio tools

3. **Packaging**:
   - Creates ZIP archives of built components
   - Generates ReadMeGuid.txt with encryption parameters
   - Uploads completed builds to C2 server

4. **Cleanup**:
   - Removes temporary files and build artifacts
   - Prepares for next build task

## Configuration

The builder is configured with:

- C2 server address and credentials
- Build templates and source code
- Compilation tools and dependencies

## Usage

The builder runs as a Windows service or console application:

```
builder.exe [--console] [--debug]
```

Options:
- `--console`: Run in console mode instead of as a service
- `--debug`: Enable verbose logging for troubleshooting

## Build Customization

Each build includes these customizable parameters:

- Public/private key pair for encryption
- Unique ticket ID for victim tracking
- Target architecture (x86/x64)
- Custom ransom amount and payment instructions
- C2 server address for victim communication