# VanHelsing Project Overview

VanHelsing is a ransomware-as-a-service (RaaS) operation consisting of multiple components that work together to:

1. Build customized ransomware payloads
2. Encrypt victim files
3. Manage victim communications
4. Process ransom payments
5. Provide decryption tools upon payment

## Architecture

The project uses a distributed architecture with these main components:

- **Windows Builder**: Compiles customized ransomware payloads
- **Locker (1-locker)**: Encrypts victim files and displays ransom notes
- **Decrypter (2-decrypter)**: Decrypts files after payment
- **Loader (3-loader)**: Loads encrypted payloads to evade detection
- **C2 Server**: Coordinates communication between components
- **Admin Panel**: Manages the operation and victim communications
- **Blog Panel**: Public leak site for data extortion

## Workflow

1. Affiliate requests a build through the admin panel
2. <PERSON><PERSON><PERSON> creates customized ransomware with unique keys
3. Affiliate deploys the ransomware to victims
4. Infected systems encrypt files and report to C2
5. Victims communicate through the chat interface
6. Upon payment, decryption keys are provided
7. If no payment, stolen data is published on the blog panel

## Technical Stack

- **Languages**: C++, PHP, JavaScript
- **Cryptography**: libsodium (XChaCha20-Poly1305, X25519, AES-256-GCM)
- **Database**: MySQL
- **Web Server**: Apache/Nginx
- **Payment**: Bitcoin