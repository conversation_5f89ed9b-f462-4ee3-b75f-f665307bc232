# Decrypter Component Documentation

The decrypter (2-decrypter) is provided to victims after payment to recover their encrypted files.

## Features

- File decryption using XChaCha20-Poly1305
- Asymmetric key decryption using X25519
- Batch processing of encrypted files
- Progress reporting and statistics

## Decryption Process

1. **Key Extraction**:
   - Reads encrypted file metadata
   - Extracts the encrypted symmetric key

2. **Key Decryption**:
   - Uses the private key to decrypt the file's symmetric key
   - The private key is only included in the decrypter after payment

3. **File Decryption**:
   - Decrypts file content using the recovered symmetric key
   - Handles both fully and partially encrypted files
   - Restores original file without the `.vanhelsing` extension

4. **Verification**:
   - Verifies decryption success using Poly1305 authentication
   - Reports statistics on successful/failed decryptions

## Usage

The decrypter provides a simple interface for victims:

1. Run the decrypter executable
2. Select directories to decrypt
3. Monitor progress through the UI
4. View summary of decryption results

## Distribution

The decrypter is:
- Built with a unique private key for each victim
- Provided only after payment verification
- Delivered through the chat interface or email