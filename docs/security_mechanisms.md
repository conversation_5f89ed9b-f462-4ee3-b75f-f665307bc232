# Security Mechanisms Documentation

This document outlines the security mechanisms implemented in the VanHelsing project.

## Authentication & Authorization

### Web Panel Authentication
- Session-based authentication with PHP sessions
- Password hashing using <PERSON><PERSON>'s password_verify()
- Random session IDs generated with bin2hex(random_bytes(64))
- Session timeout configuration (60 seconds)

### Role-Based Access Control
- Administrator: Full access to all features
- Developer: Access to build system and technical features
- Affiliate: Limited access to own victims and builds
- Support: Access to chat and basic victim information

## Cryptographic Security

### Encryption Libraries
- libsodium for all cryptographic operations
- XChaCha20-Poly1305 for file encryption
- X25519 for asymmetric key exchange
- AES-256-GCM for payload encryption

### Key Management
- Public keys distributed with ransomware
- Private keys stored securely on C2 server
- Unique keys generated for each build
- Symmetric keys unique to each encrypted file

## Database Security

### Connection Security
- PDO with prepared statements to prevent SQL injection
- Error mode set to exception handling
- Connection parameters in configuration files

### Data Protection
- Sensitive data stored with encryption
- Payment information hashed for verification
- Chat messages stored in plaintext for operator review

## Anti-Analysis Techniques

### Process Protection
- Monitors and kills security processes
- Sets high process priority to resist termination
- Disables Windows Defender and other security tools

### Persistence Mechanisms
- Creates scheduled tasks for persistence
- Adds itself to Windows startup
- Multiple persistence methods for redundancy

### Anti-Recovery
- Purges shadow copies to prevent recovery
- Disables Windows recovery options
- Deletes backup files and recovery points

## Network Security

### C2 Communication
- HTTP/HTTPS for command and control
- Configurable C2 domain and port
- Fallback servers for resilience

### Bitcoin Integration
- Bitcoin wallet integration with RPC
- Payment verification through blockchain API
- Unique addresses for each victim

## Known Security Issues

1. Hardcoded credentials in configuration files
2. Weak RPC authentication for Bitcoin
3. Insufficient input validation in some endpoints
4. Insecure file upload handling
5. Session management vulnerabilities