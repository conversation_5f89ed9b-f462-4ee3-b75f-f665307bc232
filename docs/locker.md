# Locker Component Documentation

The locker (1-locker) is the main ransomware component responsible for encrypting victim files.

## Features

- File encryption using XChaCha20-Poly1305
- Asymmetric key protection using X25519
- Shadow copy deletion to prevent recovery
- Process monitoring to kill security software
- Network spreading via SMB
- Ransom note creation and wallpaper changing

## Encryption Process

1. **File Discovery**:
   - Scans local drives and network shares
   - Identifies valuable file types based on extensions
   - Skips system directories and critical files

2. **Key Generation**:
   - Generates a unique symmetric key for each file
   - Encrypts this key with the attacker's public key

3. **File Encryption**:
   - For files <1GB: Encrypts the entire file
   - For files >1GB: Encrypts only 20% to save time
   - Adds metadata with the encrypted key
   - Appends `.vanhelsing` extension

4. **Post-Encryption Actions**:
   - Creates ransom notes on desktop
   - Changes desktop wallpaper
   - Reports to C2 server
   - Attempts to spread to other systems

## Configuration

The locker is configured at build time with:

- Unique public key for asymmetric encryption
- Unique ticket ID for victim identification
- Custom ransom amount and payment instructions
- C2 server address for communication

## Anti-Analysis Features

- Kills security processes and services
- Sets high process priority
- Creates persistence mechanisms
- Deletes shadow copies
- Disables Windows recovery options